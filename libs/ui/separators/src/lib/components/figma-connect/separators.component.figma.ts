import figma, { html } from '@figma/code-connect/html';
import { FinSeparator } from '../../enums/fin-separators';

figma.connect(
  'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=8533-23',
  {
    props: {
      type: figma.enum('Type', {
        Minimal: FinSeparator.MINIMAL,
        Strong: FinSeparator.STRONG,
        Subtle: FinSeparator.SUBTLE,
      }),
    },
    example: (props) =>
      html`<hr finHorizontalSeparator [type]="${props.type}" />`,
    imports: ["import { FinSeparatorsModule } from '@fincloud/ui/separators'"],
  },
);
