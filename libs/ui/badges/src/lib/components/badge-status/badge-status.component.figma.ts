import figma, { html } from '@figma/code-connect/html';
import { FinSize } from '@fincloud/ui/types';
import { FinBadgeStatus } from '../../enums/fin-badge-statuses';

figma.connect(
  'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=1940-70669',
  {
    props: {
      type: figma.enum('Type', {
        Draft: FinBadgeStatus.DRAFT,
        Pending: FinBadgeStatus.PENDING,
        Signed: FinBadgeStatus.SIGNED,
        Cancelled: FinBadgeStatus.CANCELLED,
        InProgress: FinBadgeStatus.IN_PROGRESS,
      }),
      size: figma.enum('Size', { S: FinSize.S, M: FinSize.M }),
    },
    example: (props) =>
      html`<fin-badge-status
        type="${props.type}"
        text="Status"
        size="${props.size}"
        iconName=""
        iconSrc=""
      ></fin-badge-status>`,
    imports: ["import { FinBadgesModule } from '@fincloud/ui/badges'"],
  },
);
