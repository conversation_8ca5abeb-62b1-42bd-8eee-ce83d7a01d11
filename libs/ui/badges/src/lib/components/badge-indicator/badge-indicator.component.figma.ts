import figma, { html } from '@figma/code-connect/html';
import { FinSize } from '@fincloud/ui/types';
import { FinBadgeType } from '../../enums/fin-badge-type';

figma.connect(
  'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=5200-48668',
  {
    props: {
      type: figma.enum('Type', {
        Default: FinBadgeType.DEFAULT,
        Active: FinBadgeType.ACTIVE,
        Attention: FinBadgeType.ATTENTION,
        Inactive: FinBadgeType.INACTIVE,
      }),
      size: figma.enum('Size', { XS: FinSize.XS, L: FinSize.L, M: FinSize.M }),
    },
    example: (props) =>
      html`<fin-badge-indicator
        count="0"
        type="${props.type}"
        size="${props.size}"
      ></fin-badge-indicator>`,
    imports: ["import { FinBadgesModule } from '@fincloud/ui/badges'"],
  },
);
