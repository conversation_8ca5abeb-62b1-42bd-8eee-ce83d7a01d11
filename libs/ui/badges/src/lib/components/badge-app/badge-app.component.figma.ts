import figma, { html } from '@figma/code-connect/html';
import { FinBadgeAppType } from '../../enums/fin-badge-app-type';

figma.connect(
  'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=3172-73350',
  {
    props: {
      type: figma.enum('AppName', {
        NextFolder: FinBadgeAppType.NEXTFOLDER,
        Dracoon: FinBadgeAppType.DRACCOON,
      }),
      status: figma.string('Type'),
    },
    example: (props: Record<string, string>) =>
      html`<fin-badge-app
        type="${props['type']}"
        status="${props['status']}"
      ></fin-badge-app> `,
    imports: ["import { FinBadgesModule } from '@fincloud/ui/badges'"],
  },
);
