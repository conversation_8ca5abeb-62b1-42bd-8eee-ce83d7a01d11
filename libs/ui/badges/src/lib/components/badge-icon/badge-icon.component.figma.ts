import figma, { html } from '@figma/code-connect/html';
import { FinSize } from '@fincloud/ui/types';
import { FinBadgeType } from '../../enums/fin-badge-type';

figma.connect(
  'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=20345-76026',
  {
    props: {
      type: figma.enum('Type', {
        Default: FinBadgeType.DEFAULT,
        Active: FinBadgeType.ACTIVE,
        Attention: FinBadgeType.ATTENTION,
        Inactive: FinBadgeType.INACTIVE,
        Ellipse: FinBadgeType.ELLIPSE,
      }),
      size: figma.enum('Size', { XS: FinSize.XS, S: FinSize.S, M: FinSize.M }),
    },
    example: (props) =>
      html`<fin-badge-icon
        type="${props.type}"
        size="${props.size}"
        name=""
        src=""
      ></fin-badge-icon>`,
    imports: ["import { FinBadgesModule } from '@fincloud/ui/badges'"],
  },
);
