import figma, { html } from '@figma/code-connect/html';
import { FinBadgeCustomerStatus } from '../../enums/fin-badge-customer-status';

figma.connect(
  'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=1325-68459',
  {
    props: {
      type: figma.enum('Type', {
        UnregisteredGuest: FinBadgeCustomerStatus.UNREGISTERED_GUEST,
        RegisteredGuest: FinBadgeCustomerStatus.REGISTERED_GUEST,
        Regular: FinBadgeCustomerStatus.REGULAR,
      }),
    },
    example: (props) =>
      html`<fin-badge-invitation type="${props.type}"></fin-badge-invitation>`,
    imports: ["import { FinBadgesModule } from '@fincloud/ui/badges'"],
  },
);
