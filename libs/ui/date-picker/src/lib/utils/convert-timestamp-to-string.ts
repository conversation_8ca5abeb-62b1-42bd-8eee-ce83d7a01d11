import { format } from 'date-fns';
import { DATE_FORMAT } from './date-format';

// Converts a timestamp to DATE_FORMAT
export function convertTimestampToString(
  selectedDate: Date | string | Date[] | string[],
) {
  if (typeof selectedDate === 'number' && !isNaN(selectedDate)) {
    return format(new Date(selectedDate), DATE_FORMAT);
  }

  if (Array.isArray(selectedDate)) {
    return selectedDate.map((date) => {
      if (typeof date === 'number' && !isNaN(date)) {
        return format(new Date(date), DATE_FORMAT);
      }
      return date;
    });
  }

  return selectedDate;
}
