import figma, { html } from '@figma/code-connect/html';
import { FinSize } from '@fincloud/ui/types';

figma.connect(
  'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=7654-10499',
  {
    props: {
      size: figma.enum('Size', {
        M: FinSize.M,
        L: FinSize.L,
      }),
    },
    example: (props) =>
      html`<fin-date-picker
        [label]="label"
        [placeholder]="placeholder"
        [size]="${props.size}"
        [selectionMode]="selectionMode"
        [view]="view"
        [showIcon]="showIcon"
        [showButtonBar]="showButtonBar"
        [disabledDates]="disabledDates"
      ></fin-date-picker>`,
    imports: ["import { FinDatePickerModule } from '@fincloud/ui/date-picker'"],
  },
);
