import figma, { html } from '@figma/code-connect/html';
import { FinSize } from '@fincloud/ui/types';

figma.connect(
  'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=22048-8189',
  {
    props: {
      size: figma.enum('Size', {
        S: FinSize.S,
        M: FinSize.M,
        L: FinSize.L,
      }),
    },
    example: (props) =>
      html`<fin-accordion
        [size]="${props.size}"
        [multi]="multi"
        [hideToggle]="hideToggle"
        [togglePosition]="togglePosition"
        [toggleDirection]="toggleDirection"
        [type]="type"
      >
        <fin-expansion-panel [disabled]="disabled" [expanded]="expanded">
          <ng-template #finTitle>Title</ng-template>

          <ng-template #finDescription>Description</ng-template>

          <ng-template #finContent>Content</ng-template>
        </fin-expansion-panel>
      </fin-accordion>`,
    imports: [
      "import { FinExpansionPanelModule } from '@fincloud/ui/expansion-panel'",
    ],
  },
);
