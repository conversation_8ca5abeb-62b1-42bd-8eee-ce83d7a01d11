import figma, { html } from '@figma/code-connect/html';
import { FinSize } from '@fincloud/ui/types';

figma.connect(
  'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=5900-6171',
  {
    props: {
      size: figma.enum('Size', {
        M: FinSize.M,
        L: FinSize.L,
      }),
    },
    example: (props) =>
      html`<fin-text-area
        [label]="label"
        [placeholder]="placeholder"
        [size]="${props.size}"
      ></fin-text-area>`,
    imports: ["import { FinTextAreaModule } from '@fincloud/ui/text-area'"],
  },
);
