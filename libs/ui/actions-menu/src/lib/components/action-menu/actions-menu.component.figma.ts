import figma, { html } from '@figma/code-connect/html';

figma.connect(
  'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=14701-7894&m=dev',
  {
    props: {},
    example: () =>
      html`<button fin-button-action [finActionMenuTrigger]="finMenu.panel">
          <fin-icon name="more_vert" [size]="size"></fin-icon>
        </button>

        <fin-actions-menu
          #finMenu="finActionMenu"
          [xPosition]="xPosition"
          [yPosition]="yPosition"
        >
          <button fin-menu-item iconName="iconName" [size]="size">
            <ng-container finMenuItemTitle>Menu item</ng-container>

            <ng-container finMenuItemDescription>Description</ng-container>

            <ng-container finMenuItemDescription>
              Additional text
            </ng-container>
          </button>
        </fin-actions-menu>`,
    imports: [
      "import { FinActionsMenuModule } from '@fincloud/ui/actions-menu'",
    ],
  },
);
