import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  Input,
  ViewChild,
} from '@angular/core';
import { MatMenu, MatMenuModule } from '@angular/material/menu';
import { FinAngularMaterialModule } from '@fincloud/utils/angular-material';
import { FinMenuPositionX } from '../../models/fin-menu-position-x';
import { FinMenuPositionY } from '../../models/fin-menu-position-y';
/**
 * A component that provides a dropdown menu for user actions.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-actions-menu--docs Storybook Reference}
 */
@Component({
  selector: 'fin-actions-menu',
  standalone: true,
  imports: [CommonModule, MatMenuModule, FinAngularMaterialModule],
  templateUrl: './actions-menu.component.html',
  styleUrl: './actions-menu.component.scss',
  host: {
    class: 'fin-action-menu',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  exportAs: 'finActionMenu',
})
export class FinActionsMenuComponent {
  /** Position of the menu in the X axis - `before` | `after` */
  @Input() yPosition: FinMenuPositionY = 'below';
  /** Position of the menu in the Y axis - `below` | `above` */
  @Input() xPosition: FinMenuPositionX = 'before';
  /** This method takes classes set on the host fin-actions-menu element and applies them on the menu template that displays in the overlay container. */
  @Input() class = '';
  /** Whether to lock scrolling when the menu is open */
  @Input() lockScroll = true;

  @ViewChild(MatMenu) panel!: MatMenu;
}
