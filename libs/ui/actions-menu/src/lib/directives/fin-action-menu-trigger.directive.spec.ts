import { DestroyRef, EventEmitter } from '@angular/core';
import { MatMenuTrigger } from '@angular/material/menu';
import { FinActionMenuTriggerDirective } from './fin-action-menu-trigger.directive';

describe('FinActionMenuTriggerDirective', () => {
  it('should create an instance', () => {
    const matMenuTriggerMock = {
      menuOpened: new EventEmitter<void>(),
      menuClosed: new EventEmitter<void>(),
      closeMenu: jest.fn(),
      menu: null,
    } as unknown as MatMenuTrigger;

    const destroyRefMock = { onDestroy: jest.fn() } as unknown as DestroyRef;

    const directive = new FinActionMenuTriggerDirective(
      matMenuTriggerMock,
      document,
      destroyRefMock,
    );
    expect(directive).toBeTruthy();
  });
});
