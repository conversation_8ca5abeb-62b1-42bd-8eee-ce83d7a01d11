import { DOCUMENT } from '@angular/common';
import {
  AfterViewInit,
  DestroyRef,
  Directive,
  Inject,
  Input,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatMenuPanel, MatMenuTrigger } from '@angular/material/menu';
import {
  filter,
  fromEvent,
  map,
  merge,
  mergeAll,
  shareReplay,
  skip,
  startWith,
  switchMap,
  tap,
} from 'rxjs';

@Directive({
  selector: '[finActionMenuTrigger]',
  standalone: true,
  hostDirectives: [
    {
      directive: MatMenuTrigger,
      inputs: ['matMenuTriggerFor: finActionMenuTrigger'],
    },
  ],
  providers: [
    {
      provide: 'MAT_MENU_TRIGGER',
      useExisting: MatMenuTrigger,
    },
  ],
  exportAs: 'finActionMenuTrigger',
})
export class FinActionMenuTriggerDirective implements AfterViewInit {
  @Input() finActionMenuTrigger!: MatMenuPanel | null;
  menuOpen = merge([
    this.matMenuTrigger.menuOpened.pipe(map(() => true)),
    this.matMenuTrigger.menuClosed.pipe(map(() => false)),
  ]).pipe(
    mergeAll(),
    startWith(false),
    shareReplay({ refCount: true, bufferSize: 1 }),
  );

  constructor(
    @Inject('MAT_MENU_TRIGGER') private matMenuTrigger: MatMenuTrigger,
    @Inject(DOCUMENT) private document: Document,
    private destroyRef: DestroyRef,
  ) {}

  ngAfterViewInit(): void {
    this.handleMenuCloseOnClickOutside();
  }

  private handleMenuCloseOnClickOutside(): void {
    this.menuOpen
      .pipe(
        filter(Boolean),
        switchMap(() =>
          fromEvent(this.document, 'click').pipe(
            skip(1), // Skip the initial click that opens the menu
            filter((event) => {
              // When lockScroll is false, the backdrop is disabled
              // so we need to manually handle clicks outside the menu to close it
              const hasBackdrop = this.matMenuTrigger.menu?.hasBackdrop;
              if (hasBackdrop) {
                // Let backdrop handle closing
                return false;
              }

              const menuPanelId = this.matMenuTrigger.menu?.panelId as string;
              const menuPanel = this.document.getElementById(menuPanelId);
              return !menuPanel?.contains(event.target as Element);
            }),
            tap(() => this.matMenuTrigger.closeMenu()),
          ),
        ),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
