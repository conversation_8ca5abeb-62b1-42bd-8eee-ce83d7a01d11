import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  ElementRef,
  EventEmitter,
  Inject,
  Injector,
  Input,
  NgZone,
  OnChanges,
  OnInit,
  Optional,
  Output,
  Renderer2,
  booleanAttribute,
  forwardRef,
  numberAttribute,
} from '@angular/core';
import { NG_VALUE_ACCESSOR } from '@angular/forms';
import { FIN_TEXT_AREA_COMPONENT_TOKEN } from '@fincloud/ui/text-area';
import { FinControlValueAccessor } from '@fincloud/utils/control-value-accessor';
import {
  BehaviorSubject,
  Observable,
  combineLatest,
  filter,
  finalize,
  interval,
  map,
  startWith,
  switchMap,
  take,
  tap,
} from 'rxjs';

/**
 * Reusable AI suggestion component that provides animated typewriter overlay functionality
 * for form controls. Shows AI suggestions with character-by-character typewriter effect on value changes.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-ai-suggestion--docs Storybook Reference}
 */
@Component({
  selector: 'fin-ai-suggestion',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './ai-suggestion.component.html',
  styleUrl: './ai-suggestion.component.scss',
  host: {
    class: 'fin-ai-suggestion',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => FinAiSuggestionComponent),
      multi: true,
    },
  ],
})
export class FinAiSuggestionComponent
  extends FinControlValueAccessor
  implements OnInit, OnChanges
{
  /**
   * Enables AI suggestion animations. When true, suggestions appear on input changes with typewriter effect.
   * Animation only triggers when BOTH `enabled` is true AND input value changes.
   */
  @Input({ transform: booleanAttribute }) enabled = false;

  /**
   * Speed of the typewriter effect in milliseconds per character.
   * Lower values make the typewriter effect faster.
   */
  @Input({ transform: numberAttribute }) typewriterSpeed = 50;

  /**
   * Emitted when the AI suggestion typewriter animation completes.
   * Only emits when the typewriter animation completes naturally, not when enabled is disabled.
   */
  @Output() aiSuggestionReady = new EventEmitter<void>();

  /** Internal BehaviorSubject to manage enabled state reactively */
  private enabledSubject$$ = new BehaviorSubject<boolean>(false);

  private readonly activeClassName = 'fin-ai-suggestion-active';

  constructor(
    injector: Injector,
    private elementRef: ElementRef,
    private renderer: Renderer2,
    private ngZone: NgZone,
    @Optional()
    @Inject(FIN_TEXT_AREA_COMPONENT_TOKEN)
    private finTextAreaComponent: any | undefined, // We use any here because we don't want to import the text-area component
  ) {
    super(injector);
  }

  ngOnInit(): void {
    this.setupAiSuggestionAnimation();
  }

  ngOnChanges(): void {
    this.enabledSubject$$.next(this.enabled);
  }

  /**
   * Sets up the AI suggestion typewriter animation logic that triggers when both
   * `enabled` is true AND the form control value changes
   */
  private setupAiSuggestionAnimation() {
    this.ngZone.runOutsideAngular(() => {
      combineLatest([this.enabledSubject$$, this.getControlValue()])
        .pipe(
          filter(([enabled]: [boolean, string | null]) => enabled === true),
          // Switch to typewriter animation for each new value
          switchMap(([, value]: [boolean, string | null]) => {
            // Handle edge cases: empty, null, or undefined values
            const textToType = value?.toString() || '';

            if (textToType.length === 0) {
              this.setEmptyString();
              return interval(1).pipe(
                take(1),
                map(() => ''),
              );
            }

            this.applyCssClasses();

            return interval(this.typewriterSpeed).pipe(
              take(textToType.length),
              map((index: number) => textToType.substring(0, index + 1)),

              tap((partialText: string) => {
                /**
                 * Updates the typewriter display with the current partial text and ensures
                 * the active CSS class is applied for styling
                 */
                this.elementRef.nativeElement.textContent = partialText;
              }),
              finalize(() => {
                this.setEmptyString();
                this.aiSuggestionReady.emit();
              }),
            );
          }),
        )
        .subscribe();
    });
  }

  /** For empty values, set empty string */
  private setEmptyString() {
    this.elementRef.nativeElement.textContent = '';
    this.removeActiveClass();
  }

  private getControlValue() {
    return this.control.valueChanges.pipe(
      startWith(this.control.value),
      // Create zone-agnostic observable to prevent change detection triggers
      // Angular's valueChanges runs in zone context, so we wrap emissions outside zone
      switchMap(
        (value) =>
          new Observable<string>((subscriber) => {
            this.ngZone.runOutsideAngular(() => {
              subscriber.next(value);
              subscriber.complete();
            });
          }),
      ),
    );
  }

  private applyCssClasses() {
    const hasClass = this.elementRef.nativeElement.classList.contains(
      this.activeClassName,
    );
    if (this.finTextAreaComponent) {
      this.renderer.addClass(
        this.elementRef.nativeElement,
        'fin-whitespace-normal',
      );
    }
    if (!hasClass) {
      this.renderer.addClass(
        this.elementRef.nativeElement,
        this.activeClassName,
      );
    }
  }

  private removeActiveClass() {
    this.renderer.removeClass(
      this.elementRef.nativeElement,
      this.activeClassName,
    );
  }
}
