import { CommonModule } from '@angular/common';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';

import { FinButtonModule } from '@fincloud/ui/button';
import { FinDatePickerModule } from '@fincloud/ui/date-picker';
import { FIN_CUSTOM_MESSAGES, FinDropdownModule } from '@fincloud/ui/dropdown';
import { FinIconModule } from '@fincloud/ui/icon';
import {
  FIN_CURRENCY_MASK,
  FIN_DATE_MASK,
  FIN_DECIMAL_MASK,
  FIN_DEFAULT_REGION_ID,
  FIN_INTEGER_MASK,
  FIN_LOCALE_ID,
  FIN_MONTHS_MASK,
  FIN_PERCENTAGE_MASK,
  FinInputModule,
  LocaleId,
} from '@fincloud/ui/input';
import { FinTextAreaModule } from '@fincloud/ui/text-area';
import {
  componentWrapperDecorator,
  moduleMetadata,
  type Meta,
  type StoryObj,
} from '@storybook/angular';
import { NgxCurrencyInputMode } from 'ngx-currency';
import { FinAiSuggestionModule } from '../../ai-suggestion.module';
import { FinAiSuggestionComponent } from './ai-suggestion.component';
const MASK_CONFIG_BASE = {
  [LocaleId.DE]: {
    align: 'left',
    allowNegative: false,
    allowZero: true,
    decimal: ',',
    thousands: '.',
    nullable: true,
    min: null,
    max: null,
    inputMode: NgxCurrencyInputMode.Natural,
    precision: 0,
    prefix: '',
    suffix: '',
  },
  [LocaleId.EN]: {
    align: 'left',
    allowNegative: false,
    allowZero: true,
    decimal: '.',
    thousands: ',',
    nullable: true,
    min: null,
    max: null,
    inputMode: NgxCurrencyInputMode.Natural,
    precision: 0,
    prefix: '',
    suffix: '',
  },
};

const CURRENCY_MASK_CONFIG = {
  [LocaleId.DE]: {
    ...MASK_CONFIG_BASE[LocaleId.DE],
    precision: 2,
    suffix: ' €',
  },
  [LocaleId.EN]: {
    ...MASK_CONFIG_BASE[LocaleId.EN],
    precision: 2,
    prefix: '€',
  },
};

const PERCENTAGE_MASK_CONFIG = {
  [LocaleId.DE]: {
    ...MASK_CONFIG_BASE[LocaleId.DE],
    precision: 3,
    suffix: ' %',
  },
  [LocaleId.EN]: {
    ...MASK_CONFIG_BASE[LocaleId.EN],
    precision: 3,
    suffix: ' %',
  },
};

const DECIMAL_MASK_CONFIG = {
  [LocaleId.DE]: {
    ...MASK_CONFIG_BASE[LocaleId.DE],
    precision: 2,
  },
  [LocaleId.EN]: {
    ...MASK_CONFIG_BASE[LocaleId.EN],
    precision: 2,
  },
};

const meta: Meta<FinAiSuggestionComponent> = {
  title: 'Fields/AI Suggestion',
  component: FinAiSuggestionComponent,
  parameters: {
    docs: {
      description: {
        component:
          '`import { FinAiSuggestionModule } from "@fincloud/ui/ai-suggestion"`',
      },
    },
  },
  decorators: [
    moduleMetadata({
      imports: [
        CommonModule,
        ReactiveFormsModule,
        FinAiSuggestionModule,
        FinButtonModule,
        FinIconModule,
        FinInputModule,
        FinDropdownModule,
        FinTextAreaModule,
        FinDatePickerModule,
      ],
      providers: [
        {
          provide: FIN_DATE_MASK,
          useValue: {
            en: {
              dateFormat: 'dd/MM/yyyy',
            },
            de: {
              dateFormat: 'dd.MM.yyyy',
            },
          },
        },
        {
          provide: FIN_DEFAULT_REGION_ID,
          useValue: LocaleId.DE,
        },
        {
          provide: FIN_LOCALE_ID,
          useValue: LocaleId.DE,
        },
        {
          provide: FIN_CURRENCY_MASK,
          useValue: CURRENCY_MASK_CONFIG,
        },
        {
          provide: FIN_PERCENTAGE_MASK,
          useValue: PERCENTAGE_MASK_CONFIG,
        },
        {
          provide: FIN_DECIMAL_MASK,
          useValue: DECIMAL_MASK_CONFIG,
        },
        {
          provide: FIN_INTEGER_MASK,
          useValue: MASK_CONFIG_BASE,
        },
        {
          provide: FIN_MONTHS_MASK,
          useValue: MASK_CONFIG_BASE,
        },
        {
          provide: FIN_CUSTOM_MESSAGES,
          useValue: {
            initialMessage: 'Enter text to search',
            noResultsMessage: 'No results found',
          },
        },
      ],
    }),
    componentWrapperDecorator(
      (story) => `<div class="fin-w-[30rem]">${story}</div>`,
    ),
  ],
};

export default meta;

type Story = StoryObj<
  FinAiSuggestionComponent & {
    form: FormGroup;
    formControl: FormControl;
  }
>;

export const Input: Story = {
  render: () => {
    const formControl = new FormControl('Some readonly data');
    const form = new FormGroup({ formControl });
    return {
      props: {
        form,
        formControl,
        suggest: () => formControl.setValue('New value'),
      },
      template: `
        <form [formGroup]="form">
          <fin-input
            type="text"
            label="Input with AI suggestion"
            formControlName="formControl"
          >
            <fin-ai-suggestion [enabled]="true" formControlName="formControl"></fin-ai-suggestion>
          </fin-input>

          <button fin-button size="m" shape="round" appearance="stealth" class="fin-mt-6" (click)="suggest()">
            <fin-icon src="assets/storybook/icons/ai.svg"></fin-icon>
            Generate
          </button>
        </form>
      `,
    };
  },
};

export const DropdownAutocomplete: Story = {
  render: () => {
    const formControl = new FormControl('');
    const form = new FormGroup({ formControl });
    const options = [
      { label: 'Berlin', value: 'berlin' },
      { label: 'Munich', value: 'munich' },
      { label: 'Hamburg', value: 'hamburg' },
    ];
    return {
      props: {
        form,
        formControl,
        options,
        suggestBerlin: () => formControl.setValue('Berlin'),
        suggestMunich: () => formControl.setValue('Munich'),
      },
      template: `
        <form [formGroup]="form">
          <fin-dropdown
            label="Autocomplete with AI suggestion"
            placeholder="Type to search"
            formControlName="formControl"
            [options]="options"
            [autocomplete]="true"
            dynamicErrorSpace="dynamic"
          >
            <fin-ai-suggestion [enabled]="true" formControlName="formControl"></fin-ai-suggestion>
          </fin-dropdown>

          <div class="fin-flex fin-gap-[0.8rem] fin-mt-6">
            <button fin-button size="m" shape="round" appearance="stealth" (click)="suggestBerlin()">
              <fin-icon src="assets/storybook/icons/ai.svg"></fin-icon>
              Suggest Berlin
            </button>
            <button fin-button size="m" shape="round" appearance="stealth" (click)="suggestMunich()">
              <fin-icon src="assets/storybook/icons/ai.svg"></fin-icon>
              Suggest Munich
            </button>
          </div>
        </form>
      `,
    };
  },
};

export const TextArea: Story = {
  render: () => {
    const formControl = new FormControl('The quick brown fox...');
    const form = new FormGroup({ formControl });
    return {
      props: {
        form,
        formControl,
        suggestShort: () => formControl.setValue('Short blurb generated by AI'),
        suggestLong: () =>
          formControl.setValue(
            'This is a longer AI-generated paragraph that demonstrates how the suggestion overlays and types into a multi-line text area to help users draft content quickly.',
          ),
      },
      template: `
        <form [formGroup]="form">
          <fin-text-area
            label="Text with AI suggestion"
            placeholder="Write something..."
            formControlName="formControl"
            [rows]="6"
          >
            <fin-ai-suggestion enabled formControlName="formControl"></fin-ai-suggestion>
          </fin-text-area>

          <div class="fin-flex fin-gap-[0.8rem] fin-mt-6">
            <button fin-button size="m" shape="round" appearance="stealth" (click)="suggestShort()">
              <fin-icon src="assets/storybook/icons/ai.svg"></fin-icon>
              Suggest short text
            </button>
            <button fin-button size="m" shape="round" appearance="stealth" (click)="suggestLong()">
              <fin-icon src="assets/storybook/icons/ai.svg"></fin-icon>
              Suggest long text
            </button>
          </div>
        </form>
      `,
    };
  },
};

export const DatePicker: Story = {
  render: () => {
    const formControl = new FormControl('15.01.2025');
    const form = new FormGroup({ formControl });
    return {
      props: {
        form,
        formControl,
        setToday: () => formControl.setValue('26.08.2025'),
        setNextWeek: () => formControl.setValue('02.09.2025'),
      },
      template: `
        <form [formGroup]="form">
          <fin-date-picker
            label="Date with AI suggestion"
            placeholder="dd/mm/yyyy"
            formControlName="formControl"
            [showIcon]="true"
          >
            <fin-ai-suggestion enabled formControlName="formControl"></fin-ai-suggestion>
          </fin-date-picker>

          <div class="fin-flex fin-gap-[0.8rem] fin-mt-6">
            <button fin-button size="m" shape="round" appearance="stealth" (click)="setToday()">
              <fin-icon src="assets/storybook/icons/ai.svg"></fin-icon>
              Suggest today
            </button>
            <button fin-button size="m" shape="round" appearance="stealth" (click)="setNextWeek()">
              <fin-icon src="assets/storybook/icons/ai.svg"></fin-icon>
              Suggest next week
            </button>
          </div>
        </form>
      `,
    };
  },
};
