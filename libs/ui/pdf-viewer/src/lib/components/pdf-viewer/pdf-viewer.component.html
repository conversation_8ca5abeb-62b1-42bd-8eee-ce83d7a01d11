<!-- <header
  #header
  class="fin-flex fin-items-center fin-justify-between fin-gap-size-spacing-24 fin-bg-color-background-neutral-minimal fin-border-b fin-border-b-color-border-default-primary fin-px-size-spacing-16 fin-py-size-spacing-12 fin-rounded-tl-size-corner-radius-m fin-rounded-tr-size-corner-radius-m"
>
  <div class="fin-text-body-1-strong fin-text-color-text-primary">
    {{ headerFilename }}
  </div>

  <button fin-button-action [size]="size.L" fin-modal-close>
    <fin-icon name="close"></fin-icon>
  </button>
</header>

<ngx-extended-pdf-viewer
  [src]="src"
  [language]="locale"
  [filenameForDownload]="filenameForDownload"
  [pageViewMode]="pageViewMode"
  [page]="page"
  [zoom]="zoom"
  [height]="pdfCalculatedHeight"
  [sidebarVisible]="sidebarVisible"
  [showToolbar]="showToolbar"
  [showSecondaryToolbarButton]="showSecondaryToolbarButton"
  [showSidebarButton]="showSidebarButton"
  [showFindButton]="showFindButton"
  [textLayer]="textLayer"
  [showPagingButtons]="showPagingButtons"
  [showZoomButtons]="showZoomButtons"
  [showOpenFileButton]="showOpenFileButton"
  [contextMenuAllowed]="contextMenuAllowed"
  [replaceBrowserPrint]="replaceBrowserPrint"
  [showHandToolButton]="showHandToolButton"
  [handTool]="handTool"
  [showRotateButton]="showRotateButton"
  [showPrintButton]="showPrintButton"
  [showDownloadButton]="showDownloadButton"
  [showPresentationModeButton]="showPresentationModeButton"
  [showSpreadButton]="showSpreadButton"
  [showPropertiesButton]="showPropertiesButton"
  [showDrawEditor]="showDrawEditor"
  [showTextEditor]="showTextEditor"
  [showStampEditor]="showStampEditor"
  [showHighlightEditor]="showHighlightEditor"
  (pdfLoaded)="onPdfLoaded()"
  (pdfDownloaded)="onPdfDownloaded()"
  (pdfLoadingFailed)="onPdfLoadingFailed($event)"
  (zoomChange)="onZoomChange($event)"
  (pageChange)="onPageChange($event)"
  (beforePrint)="onBeforePrint()"
  (afterPrint)="onAfterPrint()"
></ngx-extended-pdf-viewer>

<footer
  #footer
  class="fin-flex fin-justify-between fin-bg-color-surface-tertiary fin-text-color-text-primary fin-border-t fin-border-t-color-border-default-primary fin-px-size-spacing-16 fin-py-size-spacing-8 fin-rounded-bl-size-corner-radius-m fin-rounded-br-size-corner-radius-m"
>
  <div>
    <span class="fin-text-body-3-strong">{{ pdfMessages.uploadedBy }}: </span>
    <span class="fin-text-body-3-moderate">{{ uploadedBy }}</span>
  </div>

  <div>
    <span class="fin-text-body-3-strong">
      {{ pdfMessages.organisation }}:
    </span>
    <span class="fin-text-body-3-moderate">{{ organisation }}</span>
  </div>

  <div>
    <span class="fin-text-body-3-strong">{{ pdfMessages.source }}: </span>
    <span class="fin-text-body-3-moderate">{{ source }}</span>
  </div>
</footer> -->
