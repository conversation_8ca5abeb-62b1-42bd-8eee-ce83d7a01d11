// import { CommonModule } from '@angular/common';
// import {
//   AfterViewInit,
//   ChangeDetectionStrategy,
//   Component,
//   ElementRef,
//   EventEmitter,
//   Inject,
//   Input,
//   Optional,
//   Output,
//   ViewChild,
// } from '@angular/core';
// import { FinButtonModule } from '@fincloud/ui/button';
// import { FinIconModule } from '@fincloud/ui/icon';
// import { FinModalModule } from '@fincloud/ui/modal';
// import { FinSize } from '@fincloud/ui/types';
// import {
//   NgxExtendedPdfViewerModule,
//   NgxExtendedPdfViewerService,
// } from 'ngx-extended-pdf-viewer';
// import { FinPdfMessagesConfig } from '../../models/fin-pdf-messages-config';
// import { FIN_PDF_DEFAULT_MESSAGES } from '../../utils/fin-pdf-default-messages';
// import { FIN_PDF_MESSAGES } from '../../utils/fin-pdf-messages-token';

// /**
//  * A component for displaying pdf files.
//  *
//  * {@link https://lib-ui.neoshare.dev/?path=/docs/components-pdf-viewer--docs Storybook Reference}
//  */
// @Component({
//   selector: 'fin-pdf-viewer',
//   standalone: true,
//   imports: [
//     CommonModule,
//     FinModalModule,
//     FinButtonModule,
//     FinIconModule,
//     NgxExtendedPdfViewerModule,
//   ],
//   templateUrl: './pdf-viewer.component.html',
//   styleUrl: './pdf-viewer.component.scss',
//   changeDetection: ChangeDetectionStrategy.OnPush,
//   providers: [NgxExtendedPdfViewerService],
//   host: {
//     class: 'fin-pdf-viewer',
//   },
// })
// export class FinPdfViewerComponent implements AfterViewInit {
//   /** Document source file. */
//   @Input() src: string | ArrayBuffer | Blob | Uint8Array | URL = '';

//   /** Locale of the viewer (e.g., 'en-US', 'de-DE'). */
//   @Input() locale = '';

//   /** Filename shown in the header. */
//   @Input() headerFilename = '';

//   /** Filename to use when downloading the document. */
//   @Input() filenameForDownload = '';

//   /** Name of the user who uploaded the document (displayed in the footer). */
//   @Input() uploadedBy = '';

//   /** Name of the organisation associated with the document (displayed in the footer). */
//   @Input() organisation = '';

//   /** Source or origin of the document (displayed in the footer). */
//   @Input() source = '';

//   /** The page number to display (1-based index). */
//   @Input() page: string | number = '';

//   /** Initial zoom setting (e.g., 'auto', 'page-width', 'page-fit', or a number like 100). */
//   @Input() zoom: string | number = 'auto';

//   /** Height of the PDF viewer (CSS value, e.g. 'auto', '100vh', '100%', '800px'). */
//   @Input() height = 'auto';

//   /** Page view mode: 'single', 'multiple', or 'infinite-scroll'. */
//   @Input() pageViewMode: 'single' | 'multiple' | 'infinite-scroll' = 'multiple';

//   /** Show or hide the sidebar panel. */
//   @Input() sidebarVisible = true;

//   /** Show or hide the primary toolbar. */
//   @Input() showToolbar = true;

//   /** Show or hide the secondary toolbar button. */
//   @Input() showSecondaryToolbarButton = true;

//   /** Show or hide the sidebar toggle button. */
//   @Input() showSidebarButton = true;

//   /** Show or hide the find/search button. */
//   @Input() showFindButton = true;

//   /** Enable or disable the text layer (used for highlighting, searching, copying). */
//   @Input() textLayer = true;

//   /** Show or hide paging. */
//   @Input() showPagingButtons = true;

//   /** Show or hide zoom in/out buttons. */
//   @Input() showZoomButtons = true;

//   /** Show or hide the open file button. */
//   @Input() showOpenFileButton = false;

//   /** Allow or disable right-click context menu inside the viewer. */
//   @Input() contextMenuAllowed = false;

//   /** Replace the browser's print dialog with the viewer's own implementation. */
//   @Input() replaceBrowserPrint = true;

//   /** Show or hide the hand tool button. */
//   @Input() showHandToolButton = true;

//   /** Enable or disable the hand tool by default. */
//   @Input() handTool = true;

//   /** Show or hide the rotate button. */
//   @Input() showRotateButton = true;

//   /** Show or hide the print button. */
//   @Input() showPrintButton = true;

//   /** Show or hide the download button. */
//   @Input() showDownloadButton = true;

//   /** Show or hide the presentation mode button (fullscreen slideshow). */
//   @Input() showPresentationModeButton = true;

//   /** Show or hide the spread (two-page view) button. */
//   @Input() showSpreadButton = false;

//   /** Show or hide the document properties button. */
//   @Input() showPropertiesButton = false;

//   /** Show or hide the draw editor. */
//   @Input() showDrawEditor = true;

//   /** Show or hide the text editor. */
//   @Input() showTextEditor = true;

//   /** Show or hide the stamp editor. */
//   @Input() showStampEditor = false;

//   /** Show or hide the highlight editor. */
//   @Input() showHighlightEditor = false;

//   /** Event of type `void` emitted when the PDF has been successfully loaded and rendered. */
//   @Output() pdfLoaded = new EventEmitter<void>();

//   /** Event of type `void` emitted when the document is downloaded via the download button. */
//   @Output() pdfDownloaded = new EventEmitter<void>();

//   /** Event of type `Error` emitted if the PDF fails to load. */
//   @Output() pdfLoadingFailed = new EventEmitter<Error>();

//   /** Event of type `string | number` emitted whenever the zoom level changes. */
//   @Output() zoomChange = new EventEmitter<string | number>();

//   /** Event of type `number` emitted whenever the user navigates to a different page. */
//   @Output() pageChange = new EventEmitter<number>();

//   /** Event of type `void` emitted right before the print dialog is opened. */
//   @Output() beforePrint = new EventEmitter<void>();

//   /** Event of type `void` emitted immediately after the print dialog is closed or printing has finished. */
//   @Output() afterPrint = new EventEmitter<void>();

//   @ViewChild('header') headerRef!: ElementRef<HTMLElement>;
//   @ViewChild('footer') footerRef!: ElementRef<HTMLElement>;

//   protected size = FinSize;
//   protected pdfMessages!: FinPdfMessagesConfig;
//   protected pdfCalculatedHeight = '';

//   constructor(
//     @Optional()
//     @Inject(FIN_PDF_MESSAGES)
//     private finPdfMessages: FinPdfMessagesConfig,
//   ) {
//     this.pdfMessages = this.finPdfMessages ?? FIN_PDF_DEFAULT_MESSAGES;
//   }

//   ngAfterViewInit(): void {
//     this.calculatePdfHeight();
//   }

//   protected onPdfLoaded() {
//     this.pdfLoaded.emit();
//   }

//   protected onPdfDownloaded() {
//     this.pdfDownloaded.emit();
//   }

//   protected onPdfLoadingFailed(event: Error) {
//     this.pdfLoadingFailed.emit(event);
//   }

//   protected onZoomChange(event: string | number | undefined) {
//     this.zoomChange.emit(event);
//   }

//   protected onPageChange(event: number) {
//     this.pageChange.emit(event);
//   }

//   protected onBeforePrint() {
//     this.beforePrint.emit();
//   }

//   protected onAfterPrint() {
//     this.afterPrint.emit();
//   }

//   private calculatePdfHeight() {
//     const headerHeight = this.headerRef.nativeElement.offsetHeight;
//     const footerHeight = this.footerRef.nativeElement.offsetHeight;
//     const totalHeight = headerHeight + footerHeight;
//     this.pdfCalculatedHeight = this.height;

//     if (this.height === 'auto' || !this.height) {
//       this.pdfCalculatedHeight = '100%';
//     }

//     if (totalHeight) {
//       this.pdfCalculatedHeight = `calc(${this.pdfCalculatedHeight} - ${totalHeight}px)`;
//     }
//   }
// }
