// import { CommonModule } from '@angular/common';
// import { moduleMetadata, type Meta, type StoryObj } from '@storybook/angular';
// import { FinLayoutComponent } from '../../examples/layout/layout.component';
// import { FinPdfViewerComponent } from './pdf-viewer.component';

// const meta: Meta = {
//   component: FinPdfViewerComponent,
//   title: 'Components/Pdf viewer',
//   parameters: {
//     docs: {
//       description: {
//         component:
//           '`import { FinPdfViewerModule } from "@fincloud/ui/pdf-viewer"`',
//       },
//     },
//     design: {
//       type: 'figma',
//       url: 'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=40190-131974&m=dev',
//     },
//   },
//   decorators: [
//     moduleMetadata({
//       imports: [CommonModule, FinLayoutComponent],
//     }),
//   ],
// };
// export default meta;
// type Story = StoryObj<
//   FinPdfViewerComponent & {
//     FIN_PDF_MESSAGES: string;
//   }
// >;

// export const Primary: Story = {
//   args: {},
//   argTypes: {
//     FIN_PDF_MESSAGES: {
//       description:
//         'Custom messages used to localize the PDF viewer.<br> `FIN_PDF_MESSAGES: InjectionToken<FinPdfMessagesConfig>`',
//       table: {
//         category: 'Configs',
//       },
//     },
//   },
//   parameters: {
//     docs: {
//       source: {
//         code: `
//           <fin-modal-content>
//             <fin-pdf-viewer
//               headerFilename="Document name.pdf"
//               src="assets/storybook/pdf-viewer/example.pdf"
//               uploadedBy="user"
//               organisation="name"
//               source="uploaded"
//             >
//             </fin-pdf-viewer>
//           </fin-modal-content>
//         `,
//       },
//     },
//   },
//   render: () => ({
//     template: `
//       <fin-layout></fin-layout>
//     `,
//   }),
// };
