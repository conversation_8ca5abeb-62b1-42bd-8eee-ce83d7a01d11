:host {
  ::ng-deep {
    ngx-extended-pdf-viewer {
      .pageNumber,
      .toolbarLabel,
      #scaleSelectContainer select,
      #scaleSelectContainer option {
        @apply fin-text-body-2-moderate #{!important};
        @apply fin-text-color-text-primary #{!important};
      }

      #numPages {
        @apply fin-mx-size-spacing-8;
      }

      #thumbnailView {
        overflow-x: hidden !important;
      }

      #viewer,
      #sidebarContent {
        @apply fin-bg-color-surface-tertiary #{!important};
      }

      #toolbarViewerLeft,
      #toolbarViewerRight,
      pdf-paging-area,
      pdf-toggle-secondary-toolbar {
        @apply fin-flex;
        @apply fin-items-center;
      }
    }
  }
}
