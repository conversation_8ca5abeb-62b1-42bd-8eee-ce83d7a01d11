// import { CommonModule } from '@angular/common';
// import { ChangeDetectionStrategy, Component } from '@angular/core';
// import { FinModalModule } from '@fincloud/ui/modal';
// import { FinSize } from '@fincloud/ui/types';
// import { FinPdfViewerModule } from '../../pdf-viewer.module';

// @Component({
//   selector: 'fin-full-example',
//   standalone: true,
//   imports: [CommonModule, FinPdfViewerModule, FinModalModule],
//   templateUrl: './full-example.component.html',
//   changeDetection: ChangeDetectionStrategy.OnPush,
// })
// export class FinFullExampleComponent {
//   size = FinSize;
// }
