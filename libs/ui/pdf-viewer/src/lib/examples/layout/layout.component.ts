// import { CommonModule } from '@angular/common';
// import { ChangeDetectionStrategy, Component } from '@angular/core';
// import { FinButtonModule } from '@fincloud/ui/button';
// import { FinModalService } from '@fincloud/ui/modal';
// import { FinFullExampleComponent } from '../full-example/full-example.component';
// import { FinSize } from '@fincloud/ui/types';

// @Component({
//   selector: 'fin-layout',
//   standalone: true,
//   imports: [CommonModule, FinButtonModule],
//   templateUrl: './layout.component.html',
//   changeDetection: ChangeDetectionStrategy.OnPush,
// })
// export class FinLayoutComponent {
//   constructor(private modal: FinModalService) {}

//   openPdfModal() {
//     this.modal.open(FinFullExampleComponent, {
//       size: FinSize.XXL,
//     });
//   }
// }
