import figma, { html } from '@figma/code-connect/html';
import { FinEmptyStateType } from '../../enums/fin-empty-state-type';

figma.connect(
  'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=6708-9748',
  {
    props: {
      type: figma.enum('Type', {
        Basic: FinEmptyStateType.BASIC,
        Complex: FinEmptyStateType.COMPLEX,
      }),
    },
    example: (props) =>
      html` <fin-empty-state [type]="${props.type}" title="">
        <ng-template #finIcon>Icon</ng-template>

        <ng-template #finTextContent>Content</ng-template>

        <ng-template #finDescription>Description</ng-template>

        <ng-template #finActions>Actions</ng-template>
      </fin-empty-state>`,
    imports: ["import { FinEmptyStateModule } from '@fincloud/ui/empty-state'"],
  },
);
