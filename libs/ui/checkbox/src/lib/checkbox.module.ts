import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FinMarkAllAsTouchedDirective } from '@fincloud/utils/directives';
import { FinFormService } from '@fincloud/utils/services';
import { FinCheckboxComponent } from './components/checkbox.component';
import { FinIndeterminateDirective } from './directives/indeterminate.directive';

@NgModule({
  imports: [
    CommonModule,
    FinCheckboxComponent,
    FinIndeterminateDirective,
    FinMarkAllAsTouchedDirective,
  ],
  exports: [
    FinCheckboxComponent,
    FinIndeterminateDirective,
    FinMarkAllAsTouchedDirective,
  ],
  providers: [FinFormService],
})
export class FinCheckboxModule {}
