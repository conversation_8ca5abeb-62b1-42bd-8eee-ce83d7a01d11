import figma, { html } from '@figma/code-connect/html';
import { FinSize, LabelPosition } from '@fincloud/ui/types';

figma.connect(
  'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=5375-814',
  {
    props: {
      size: figma.enum('Size', {
        S: FinSize.S,
        M: FinSize.M,
      }),
      labelPosition: figma.enum('Label Position', {
        Left: LabelPosition.AFTER,
        Right: LabelPosition.BEFORE,
      }),
    },
    example: (props) =>
      html`<fin-checkbox
        [label]="label"
        [size]="${props.size}"
        [labelPosition]="${props.labelPosition}"
      ></fin-checkbox>`,
    imports: ["import { FinCheckboxModule } from '@fincloud/ui/checkbox'"],
  },
);
