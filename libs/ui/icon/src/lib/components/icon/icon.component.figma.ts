import figma, { html } from '@figma/code-connect/html';
import { FinSize } from '@fincloud/ui/types';

figma.connect(
  'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=18555-19463',
  {
    props: {
      size: figma.enum('Size', {
        'XS (12)': FinSize.XS,
        'S (16)': FinSize.S,
        'M (20)': FinSize.M,
        'L (24)': FinSize.L,
        'XL (32)': FinSize.XL,
        'XXL (48)': FinSize.XXL,
      }),
    },
    example: (props) =>
      html`<fin-icon [size]="${props.size}" name="account_circle"></fin-icon>`,
    imports: ["import { FinIconModule } from '@fincloud/ui/icon'"],
  },
);
