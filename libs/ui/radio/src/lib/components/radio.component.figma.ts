import figma, { html } from '@figma/code-connect/html';
import { FinSize } from '@fincloud/ui/types';

figma.connect(
  'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=5757-3587',
  {
    props: {
      size: figma.enum('Size', {
        S: FinSize.S,
        M: FinSize.M,
      }),
    },
    example: (props) =>
      html`<fin-radio-button
        label="Label"
        [options]="options"
        [size]="${props.size}"
      ></fin-radio-button>`,
    imports: ["import { FinRadioModule } from '@fincloud/ui/radio'"],
  },
);
