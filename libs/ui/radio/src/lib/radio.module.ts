import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FinRadioComponent } from './components/radio.component';
import { FinMarkAllAsTouchedDirective } from '@fincloud/utils/directives';
import { FinFormService } from '@fincloud/utils/services';

@NgModule({
  imports: [CommonModule, FinRadioComponent, FinMarkAllAsTouchedDirective],
  exports: [FinRadioComponent, FinMarkAllAsTouchedDirective],
  providers: [FinFormService],
})
export class FinRadioModule {}
