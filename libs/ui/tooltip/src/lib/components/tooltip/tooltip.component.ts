import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  ElementRef,
  EmbeddedViewRef,
  Input,
  numberAttribute,
  OnInit,
  Renderer2,
  TemplateRef,
} from '@angular/core';
import { pxToRem } from '@fincloud/utils/functions';
import { POSITION_STRATEGIES } from '../../utils/positions';

@Component({
  selector: 'fin-tooltip',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './tooltip.component.html',
  styleUrl: './tooltip.component.scss',
  host: {
    '[class.fin-tooltip]': '!!content',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinTooltipComponent implements OnInit {
  /** string or TemplateRef */
  @Input() content!: string | TemplateRef<unknown>;
  @Input() context!: EmbeddedViewRef<unknown>;
  @Input() showArrow = false;

  /** current side: 'top' | 'right' | 'bottom' | 'left' */
  @Input() position: keyof typeof POSITION_STRATEGIES = 'top';

  /** Optional px limit injected by directive */
  @Input({ transform: numberAttribute }) maxWidth: number | null = null;

  constructor(
    private renderer: Renderer2,
    private elementRef: ElementRef<HTMLElement>,
  ) {}

  ngOnInit(): void {
    if (this.maxWidth) {
      this.setMaxWidth(this.maxWidth);
    }
  }

  private setMaxWidth(maxWidth: number): void {
    this.renderer.setStyle(
      this.elementRef.nativeElement,
      'max-width',
      pxToRem(maxWidth),
    );
  }

  isTemplate(
    value: string | TemplateRef<unknown>,
  ): value is TemplateRef<unknown> {
    return value instanceof TemplateRef;
  }
}
