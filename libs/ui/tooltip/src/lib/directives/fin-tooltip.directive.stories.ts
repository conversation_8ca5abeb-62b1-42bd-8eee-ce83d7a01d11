import { BrowserModule } from '@angular/platform-browser';
import { FinButtonModule } from '@fincloud/ui/button';
import {
  Meta,
  StoryObj,
  argsToTemplate,
  componentWrapperDecorator,
  moduleMetadata,
} from '@storybook/angular';
import { FinTooltipModule } from '../tooltip.module';

const meta: Meta = {
  title: 'Directives/Tooltip',
  parameters: {
    docs: {
      description: {
        component: '`import { FinTooltipModule } from "@fincloud/ui/tooltip"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=9662-7005',
    },
  },
  decorators: [
    moduleMetadata({
      imports: [BrowserModule, FinTooltipModule, FinButtonModule],
    }),
    componentWrapperDecorator(
      (story) =>
        `<div style="width: 300px; display: flex; justify-content: center">${story}</div>`,
    ),
  ],
  argTypes: {
    content: {
      description:
        'The string content or a TemplateRef for the content to be displayed in the tooltip.',
    },
    context: {
      description:
        'Default template context for TemplateRef, for example `[context]={ [index as string]: any }`',
    },
    container: {
      description:
        'A selector specifying the element the tooltip should be appended to.',
      defaultValue: { summary: 'body' },
    },
    showArrow: {
      options: [true, false],
      description: 'Display an arrow icon..',
      defaultValue: false,
      control: { type: 'radio' },
      table: {
        defaultValue: { summary: 'false' },
      },
    },
    setAlwaysVisible: {
      options: [true, false],
      description: 'Manually set tooltip to be always visible.',
      defaultValue: false,
      control: { type: 'radio' },
      table: {
        defaultValue: { summary: 'false' },
      },
    },
    placement: {
      options: ['top', 'bottom', 'left', 'right'],
      description:
        'Specifies the placement of the tooltip relative to the element.',
      table: {
        defaultValue: { summary: 'top' },
      },
      control: { type: 'select' },
    },
    positionTarget: {
      description:
        'CSS selector by ID for an element to anchor the tooltip to (instead of the host)',
    },
    maxWidth: {
      description: 'Manually set the max width (in rems) for the tooltip.',
      control: { type: 'number' },
      table: {
        defaultValue: { summary: '24' },
      },
    },
    openDelay: {
      description:
        'Specifies the delay in milliseconds before the tooltip is displayed.',
      defaultValue: { summary: 400 },
    },
    closeDelay: {
      description:
        'Specifies the delay in milliseconds before the tooltip is hidden.',
    },
  },
  args: {
    showArrow: false,
    setAlwaysVisible: false,
  },
  render: (args) => ({
    template: `
   <button type="button" finTooltip ${argsToTemplate(args)}> Tooltip</button> `,
    props: args,
  }),
};

export default meta;
type Story = StoryObj;

export const Top: Story = {
  args: {
    placement: 'top',
    content: 'Tooltip',
  },
};
export const Bottom: Story = {
  args: {
    placement: 'bottom',
    content: 'Tooltip',
  },
};

export const Left: Story = {
  args: {
    placement: 'left',
    content: 'Tooltip',
  },
};

export const Right: Story = {
  args: {
    placement: 'right',
    content: 'Tooltip',
  },
};

export const TopWithArrow: Story = {
  args: {
    ...Top.args,
    showArrow: true,
  },
};

export const BottomWithArrow: Story = {
  args: {
    ...Bottom.args,
    showArrow: true,
  },
};

export const LeftWithArrow: Story = {
  args: {
    ...Left.args,
    showArrow: true,
  },
};

export const RightWithArrow: Story = {
  args: {
    ...Right.args,
    showArrow: true,
  },
};

export const WithHtml: Story = {
  args: {
    placement: 'left',
    showArrow: true,
  },
  render: (args) => ({
    template: `
    <button type="button" finTooltip [content]="test" ${argsToTemplate(
      args,
    )}>Tooltip</button>
    <ng-template #test>
      <b class="fin-text-left">Lorem Ipsum is simply dummy text</b>
      <div>Lorem Ipsum has been the industry's standard dummy text ever since the 1500s</div>
    </ng-template>
   `,
    props: args,
  }),
};

export const WithTemplateContext: Story = {
  args: {
    placement: 'left',
    showArrow: true,
  },
  render: (args) => ({
    props: {
      ...args,
      items: [
        {
          label: 'apple',
          text: 'fruit 1',
        },
        {
          label: 'pear',
          text: 'fruit 2',
        },
        {
          label: 'plum',
          text: 'fruit 3',
        },
      ],
    },
    template: `
    <div>
      <ng-container *ngFor="let item of items">
        <button type="button" finTooltip [content]="test" [context]="{text: item.text}" ${argsToTemplate(
          args,
        )}>{{item.label}}</button>
        <div class="fin-my-4"></div>
      </ng-container>
    </div>
    <ng-template #test let-text="text">
      <div>{{text}}</div>
    </ng-template>
   `,
  }),
};

export const WithDelay: Story = {
  render: () => ({
    template: `
      <div class="fin-flex fin-gap-[3rem]">
        <div
          finTooltip
          content="Show with delay"
          [openDelay]="1000"
        >
          Show with delay
        </div>

        <div
          finTooltip
          content="Close with delay"
          [closeDelay]="1000"
        >
          Close with delay
        </div>
      </div>
   `,
  }),
};

export const PositionTarget: Story = {
  render: () => ({
    template: `
      <div class="fin-grid fin-grid-cols-3 fin-gap-4">
        <div class="fin-flex fin-items-center fin-justify-center">
          <div id="target1">Target 1</div>
        </div>       
        <div finTooltip positionTarget="target1" content="Target 1 tooltip" class="fin-col-span-2">
          Hover over me to show a tooltip on Target 1 (by element reference).
        </div>

        <div class="fin-flex fin-items-center fin-justify-center">
          <button fin-button size="s" id="target2">Target 2</button>
        </div>
        <div finTooltip positionTarget="target2" content="Target 2 tooltip" placement="left" class="fin-col-span-2">
          Hover over me to show a tooltip on Target 2 (by element id).
        </div>

        <div class="fin-flex fin-items-center fin-justify-center">
          <button fin-button fin-button size="s" id="target3">Target 3</button>
        </div>        
        <div finTooltip positionTarget="target3" content="Target 3 tooltip" placement="bottom" class="fin-col-span-2">
          Hover over me to show a tooltip on Target 3 (by element class).
        </div>
      </div>
   `,
  }),
};
