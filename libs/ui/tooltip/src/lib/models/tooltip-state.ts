import {
  FlexibleConnectedPositionStrategy,
  OverlayRef,
} from '@angular/cdk/overlay';
import { ComponentPortal } from '@angular/cdk/portal';
import { ComponentRef, TemplateRef } from '@angular/core';
import { FinTooltipComponent } from '../components/tooltip/tooltip.component';
import { POSITION_STRATEGIES } from '../utils/positions';

export type TooltipState = {
  overlayReference: OverlayRef | null;

  /** The CDK position strategy used to calculate overlay position */
  positionStrategy: FlexibleConnectedPositionStrategy;

  /** Cache of the container element to avoid repeated DOM moves */
  cachedContainerElement: HTMLElement | null;

  mouseInside: boolean;

  focus: boolean;
  componentReference: ComponentRef<FinTooltipComponent> | null;
  componentPortal: ComponentPortal<FinTooltipComponent>;

  /** The tooltip content: string, raw HTML, or a TemplateRef  */
  content: string | TemplateRef<unknown> | null | undefined;

  /** Optional context object supplied when `content` is a `TemplateRef` */
  context: Record<string, unknown> | null;

  /** When set, overrides tooltipPosition  */
  placement: keyof typeof POSITION_STRATEGIES;

  /** Delay (ms) before showing tooltip on hover/focus */
  openDelay: number;

  /** Delay (ms) before hiding tooltip on mouse leave/blurs */
  closeDelay: number;

  /** If true, tooltip opens immediately on init and stays visible until disabled */
  setAlwaysVisible: boolean;

  /** If true, tooltip is disabled and never shows */
  disableTooltip: boolean;

  /** Maximum width (px) for the tooltip bubble */
  maxWidth: number | null;

  /** CSS selector for an element to anchor the tooltip to (instead of the host) */
  positionTarget: string | null;

  showArrow: boolean;

  /** Indicates whether an element is currently visible or hidden on the page. */
  isVisible: boolean;
};
