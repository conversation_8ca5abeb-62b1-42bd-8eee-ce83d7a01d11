:host {
  display: block;

  .fin-border-sweep {
    &-container {
      height: 100%;
      border-radius: inherit;

      & > div {
        border-radius: inherit;
      }
    }

    &-animation-on {
      position: relative;
      z-index: 0;
      overflow: hidden;
      contain: paint;
      isolation: isolate;
      &::before {
        content: '';
        position: absolute;
        border-radius: inherit;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        z-index: -1;
        background: linear-gradient(
          to right,
          #060fd0,
          #03086a,
          #8e1ccc,
          #556bff,
          #198aff,
          #7587ff
        );
        will-change: transform;
        pointer-events: none;
        animation: borderSweepTranslate var(--animation-duration) ease-in-out
          infinite alternate;
        animation-delay: var(--animation-delay);
      }
    }
  }
}

@keyframes borderSweepTranslate {
  from {
    transform: translateX(0%);
  }
  to {
    transform: translateX(100%);
  }
}

@media (prefers-reduced-motion: reduce) {
  .fin-border-sweep-animation-on::before {
    animation: none !important;
  }
}
