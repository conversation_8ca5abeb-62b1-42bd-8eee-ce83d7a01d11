import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FinMarkAllAsTouchedDirective } from '@fincloud/utils/directives';
import { FinFormService } from '@fincloud/utils/services';
import { FinSwitchToggleComponent } from './components/switch-toggle/switch-toggle.component';

@NgModule({
  imports: [
    CommonModule,
    FinSwitchToggleComponent,
    FinMarkAllAsTouchedDirective,
  ],
  exports: [FinSwitchToggleComponent, FinMarkAllAsTouchedDirective],
  providers: [FinFormService],
})
export class FinSwitchToggleModule {}
