import figma, { html } from '@figma/code-connect/html';

figma.connect(
  'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=6339-5606',
  {
    props: {},
    example: () =>
      html`<fin-table
        [rows]="rows"
        [columns]="columns"
        [headerHeight]="44"
        [rowHeight]="64"
        [hasRowBorder]="false"
      >
        <ng-template name="name" [finRowTemplate]="rows" let-row>
          {{ row.person.fName }} {{ row.person.lName }}
        </ng-template>

        <ng-template name="capital" [finRowTemplate]="rows" let-row>
          {{ row.capital | currency: 'EUR' }}
        </ng-template>

        <ng-template name="info" [finRowTemplate]="rows" let-row>
          {{ row.info }}
        </ng-template>

        <ng-template name="grade" [finRowTemplate]="rows" let-row>
          {{ row.grade }}
        </ng-template>
      </fin-table>`,
    imports: ["import { FinTableModule } from '@fincloud/ui/table'"],
  },
);
