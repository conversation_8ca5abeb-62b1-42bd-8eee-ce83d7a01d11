import figma, { html } from '@figma/code-connect/html';
import { FinWarningMessageAppearance } from '../enums/fin-warning-message-appearance';

figma.connect(
  'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=6083-224',
  {
    props: {
      appearance: figma.enum('Type', {
        Attention: FinWarningMessageAppearance.ATTENTION,
        Basic: FinWarningMessageAppearance.BASIC,
        Informative: FinWarningMessageAppearance.INFORMATIVE,
        Neutral: FinWarningMessageAppearance.NEUTRAL,
        Success: FinWarningMessageAppearance.SUCCESS,
        Warning: FinWarningMessageAppearance.WARNING,
      }),
    },
    example: (props) =>
      html`<fin-warning-message
        [label]="label"
        [showIcon]="showIcon"
        [appearance]="${props.appearance}"
      ></fin-warning-message>`,
    imports: [
      "import { FinWarningMessageModule } from '@fincloud/ui/warning-message'",
    ],
  },
);
