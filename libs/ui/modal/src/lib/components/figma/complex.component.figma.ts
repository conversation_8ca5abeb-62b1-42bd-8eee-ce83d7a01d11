import figma, { html } from '@figma/code-connect/html';

figma.connect(
  'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=21816-3589',
  {
    props: {},
    example: () =>
      html`<fin-modal-header>
          <fin-header>
            Header

            <button fin-button-action size="l" fin-modal-close>
              <fin-icon name="close"></fin-icon>
            </button>
          </fin-header>
        </fin-modal-header>

        <fin-modal-slots-container>
          <fin-modal-slot [size]="size.S">Slot size S</fin-modal-slot>
          <fin-modal-slot [size]="size.L">Slot size L</fin-modal-slot>
        </fin-modal-slots-container>

        <fin-modal-footer separator>
          <fin-footer>
            <button
              fin-button
              fin-modal-close
              [appearance]="appearance.SECONDARY"
            >
              Close
            </button>

            <button fin-button [appearance]="appearance.PRIMARY">Action</button>
          </fin-footer>
        </fin-modal-footer>`,
    imports: ["import { FinModalModule } from '@fincloud/ui/modal'"],
  },
);
