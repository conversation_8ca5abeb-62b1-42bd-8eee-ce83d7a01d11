import figma, { html } from '@figma/code-connect/html';
import { FinSize } from '@fincloud/ui/types';

figma.connect(
  'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=7116-7318&',
  {
    props: {
      // type: figma.enum('Type', {
      //   Primary: FinTabType.PRIMARY,
      //   Secondary: FinTabType.SECONDARY,
      // }),
      size: figma.enum('Size', {
        XL: FinSize.XL,
        L: FinSize.L,
      }),
    },
    example: (props) =>
      html`<fin-tabs type="secondary" size="${props.size}"></fin-tabs>`,
    imports: ["import { FinTabsModule } from '@fincloud/ui/tabs'"],
  },
);
