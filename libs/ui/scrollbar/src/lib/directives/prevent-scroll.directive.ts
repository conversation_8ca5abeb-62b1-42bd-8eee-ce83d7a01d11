import { DestroyRef, Directive, ElementRef, OnInit } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { NEVER, fromEvent, switchMap } from 'rxjs';
import { FinScrollbarService } from '../service/fin-scrollbar.service';

@Directive({
  selector: '[finPreventScroll]',
  standalone: true,
})
export class FinPreventScrollDirective implements OnInit {
  constructor(
    private elementRef: ElementRef,
    private destroyRef: DestroyRef,
    private finScrollbarService: FinScrollbarService,
  ) {}

  ngOnInit(): void {
    this.finScrollbarService.disabled$
      .pipe(
        switchMap((disabled) =>
          disabled
            ? fromEvent<WheelEvent>(this.elementRef.nativeElement, 'wheel')
            : NEVER,
        ),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe((event) => this.preventScroll(event));
  }
  /**
   * Prevents the default scroll behavior on the element.
   * @param event The scroll event to prevent.
   */
  private preventScroll(event: Event) {
    event.preventDefault();
    event.stopImmediatePropagation();
    return false;
  }
}
