import { moduleMetadata, type Meta, type StoryObj } from '@storybook/angular';

import { CommonModule } from '@angular/common';
import { FormControl, ReactiveFormsModule, Validators } from '@angular/forms';
import { FinCheckboxModule } from '@fincloud/ui/checkbox';
import {
  FIN_CURRENCY_MASK,
  FIN_DECIMAL_MASK,
  FIN_INTEGER_MASK,
  FIN_MONTHS_MASK,
  FIN_PERCENTAGE_MASK,
  FinInputModule,
  LocaleId,
} from '@fincloud/ui/input';
import { FinRadioModule } from '@fincloud/ui/radio';
import { FinFieldMessagesType } from '@fincloud/ui/types';
import { FinFieldService } from '@fincloud/utils/services';
import { NgxCurrencyInputMode } from 'ngx-currency';
import { FinFieldMessageModule } from '../../field-message.module';
import { FinFieldMessagesComponent } from './field-messages.component';

const MASK_CONFIG_BASE = {
  [LocaleId.DE]: {
    align: 'left',
    allowNegative: false,
    allowZero: true,
    decimal: ',',
    thousands: '.',
    nullable: true,
    min: null,
    max: null,
    inputMode: NgxCurrencyInputMode.Natural,
    precision: 0,
    prefix: '',
    suffix: '',
  },
  [LocaleId.EN]: {
    align: 'left',
    allowNegative: false,
    allowZero: true,
    decimal: '.',
    thousands: ',',
    nullable: true,
    min: null,
    max: null,
    inputMode: NgxCurrencyInputMode.Natural,
    precision: 0,
    prefix: '',
    suffix: '',
  },
};

const CURRENCY_MASK_CONFIG = {
  [LocaleId.DE]: {
    ...MASK_CONFIG_BASE[LocaleId.DE],
    precision: 2,
    suffix: ' €',
  },
  [LocaleId.EN]: {
    ...MASK_CONFIG_BASE[LocaleId.EN],
    precision: 2,
    prefix: '€',
  },
};

const PERCENTAGE_MASK_CONFIG = {
  [LocaleId.DE]: {
    ...MASK_CONFIG_BASE[LocaleId.DE],
    precision: 3,
    suffix: ' %',
  },
  [LocaleId.EN]: {
    ...MASK_CONFIG_BASE[LocaleId.EN],
    precision: 3,
    suffix: ' %',
  },
};
const DECIMAL_MASK_CONFIG = {
  [LocaleId.DE]: {
    ...MASK_CONFIG_BASE[LocaleId.DE],
    precision: 2,
  },
  [LocaleId.EN]: {
    ...MASK_CONFIG_BASE[LocaleId.EN],
    precision: 2,
  },
};

const meta: Meta<FinFieldMessagesComponent> = {
  component: FinFieldMessagesComponent,
  title: 'Fields/Field Messages',
  parameters: {
    docs: {
      description: {
        component:
          '`import { FinFieldMessageModule } from "@fincloud/ui/field-message"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-WIP?type=design&node-id=6528-194&mode=dev',
    },
  },
  decorators: [
    moduleMetadata({
      imports: [
        FinInputModule,
        ReactiveFormsModule,
        CommonModule,
        FinFieldMessageModule,
        FinRadioModule,
        FinCheckboxModule,
      ],
      providers: [
        FinFieldService,
        {
          provide: FIN_CURRENCY_MASK,
          useValue: CURRENCY_MASK_CONFIG,
        },
        {
          provide: FIN_PERCENTAGE_MASK,
          useValue: PERCENTAGE_MASK_CONFIG,
        },
        {
          provide: FIN_DECIMAL_MASK,
          useValue: DECIMAL_MASK_CONFIG,
        },
        {
          provide: FIN_INTEGER_MASK,
          useValue: MASK_CONFIG_BASE,
        },
        {
          provide: FIN_MONTHS_MASK,
          useValue: MASK_CONFIG_BASE,
        },
      ],
    }),
  ],
};
export default meta;
type Story = StoryObj<
  FinFieldMessagesComponent & {
    finFieldMessage: string;
    errorKey: string;
    type: FinFieldMessagesType;
    priority: number;
  }
>;

export const Primary: Story = {
  argTypes: {
    finFieldMessage: {
      description: 'A `Directive` used with `ng-template`.',
      table: {
        category: 'Directive',
      },
    },
    errorKey: {
      description:
        'The key corresponding to the validation error (e.g. `required`, `minlength`, etc.)',
      table: {
        category: 'Directive',
      },
      control: 'text',
    },
    type: {
      description: 'The message type.',
      table: {
        category: 'Directive',
      },
      control: 'select',
      options: ['error', 'warning', 'success'],
    },
    priority: {
      description:
        'Optional priority; If omitted the priority is set by the order of `finFieldMessage` in the template.',
      table: {
        category: 'Directive',
      },
      control: 'number',
    },
    getSelectedMessage$: {
      table: {
        defaultValue: {
          summary: 'Observable<FinFieldMessageDirective>',
        },
      },
    },
  },
  render: (args) => {
    const formControl = new FormControl('', [
      Validators.required,
      Validators.minLength(4),
    ]);
    formControl.markAsTouched();
    formControl.updateValueAndValidity();

    return {
      props: { ...args, formControl },
      template: `
      <div class="fin-w-[250px] fin-m-10">
        <fin-input
          [formControl]="formControl"
          [label]="'Input label'"
        >
          <fin-field-messages>
            <ng-template finFieldMessage type="warning" errorKey="minlength"
              >Value should be min 4 characters.</ng-template
            >
            <ng-template finFieldMessage type="error" errorKey="required"
              >This field is required.</ng-template
            >

            <ng-template finFieldMessage type="success">Success</ng-template>
          </fin-field-messages>
        </fin-input>
      </div>
    `,
    };
  },
};

export const OnlyBorder: Story = {
  argTypes: {
    ...Primary.argTypes,
  },
  render: (args) => {
    const formControl = new FormControl('', [
      Validators.required,
      Validators.minLength(4),
    ]);
    formControl.markAsTouched();
    formControl.updateValueAndValidity();

    return {
      props: { ...args, formControl },
      template: `
      <div class="fin-w-[250px] fin-m-10">
        <fin-input
          [formControl]="formControl"
          [label]="'Input label'"
        >
          <fin-field-messages>
            <ng-template finFieldMessage type="warning" errorKey="minlength"></ng-template
            >
            <ng-template finFieldMessage type="error" errorKey="required"></ng-template
            >
            <ng-template finFieldMessage type="success"></ng-template>
          </fin-field-messages>
        </fin-input>
      </div>
    `,
    };
  },
};

export const ExternalMessage: Story = {
  argTypes: {
    ...Primary.argTypes,
  },
  render: (args) => {
    const formControl = new FormControl('', [
      Validators.required,
      Validators.minLength(4),
    ]);
    formControl.markAsTouched();
    formControl.updateValueAndValidity();
    return {
      props: { ...args, formControl },
      template: `
      <div class="fin-w-[250px] fin-m-10">
         <!-- Field -->
        <fin-input
          [formControl]="formControl"
          [label]="'Input label'"
          [externalFieldMessages]="validationMassages.getSelectedMessage$ | async"
        >
        </fin-input>

        <!-- Field Messages -->
        <fin-field-messages #validationMassages [formControl]="formControl">
          <ng-template finFieldMessage type="warning" errorKey="minlength"
            >Value should be min 4 characters.</ng-template
          >
          <ng-template finFieldMessage type="error" errorKey="required"
            >This field is required.</ng-template
          >

          <ng-template finFieldMessage type="success">Success</ng-template>
        </fin-field-messages>
      </div>
    `,
    };
  },
};

export const RadioButton: Story = {
  argTypes: {
    ...Primary.argTypes,
  },
  render: (args) => {
    const formControl = new FormControl('', [Validators.requiredTrue]);
    formControl.markAsTouched();
    formControl.updateValueAndValidity();
    return {
      props: { ...args, formControl },
      template: `
      <div class="fin-w-[250px] fin-m-10">
        <fin-radio-button
          [formControl]="formControl"
          [options]="[
            { value: true, label: 'First option' },
            { value: false, label: 'Second option' },
          ]"
        ></fin-radio-button>

        <fin-field-messages [formControl]="formControl">
          <ng-template finFieldMessage type="error" errorKey="required"
            >This field is required.</ng-template
          >
        </fin-field-messages>
      </div>
    `,
    };
  },
};

export const Checkbox: Story = {
  argTypes: {
    ...Primary.argTypes,
  },
  render: (args) => {
    const formControl = new FormControl('', [Validators.requiredTrue]);
    formControl.markAsTouched();
    formControl.updateValueAndValidity();
    return {
      props: { ...args, formControl },
      template: `
      <div class="fin-w-[250px] fin-m-10">
        <fin-checkbox
          [formControl]="formControl"
          [label]="'Option label'"
        ></fin-checkbox>

        <fin-field-messages [formControl]="formControl">
          <ng-template finFieldMessage type="error" errorKey="required"
            >This field is required.</ng-template
          >
        </fin-field-messages>
      </div>
    `,
    };
  },
};
