import { BrowserModule } from '@angular/platform-browser';
import { FinButtonModule } from '@fincloud/ui/button';
import { FinIconModule } from '@fincloud/ui/icon';
import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { FinAIShadowEffectModule } from '../ai-shadow-effect.module';
import { FinAIShadowEffectComponent } from './ai-shadow-effect.component';

const meta: Meta<FinAIShadowEffectComponent> = {
  title: 'Components/AI Shadow Effect',
  parameters: {
    docs: {
      description: {
        component:
          '`import { FinAIShadowEffectModule } from "@fincloud/ui/ai-shadow-effect"`',
      },
    },
  },
  decorators: [
    moduleMetadata({
      imports: [
        BrowserModule,
        FinIconModule,
        FinButtonModule,
        FinAIShadowEffectModule,
      ],
    }),
  ],
};

export default meta;

type Story = StoryObj<FinAIShadowEffectComponent>;

export const AIShadowEffect: Story = {
  args: {
    finAiShadowEffect: true,
  },
  render: (args) => ({
    props: args,
    template: `
      <div class="fin-h-[40rem] fin-w-[40rem] fin-flex fin-justify-center fin-items-center">
        <div class="fin-relative" [finAiShadowEffect]="finAiShadowEffect">
          <button fin-button-fab size="m">
            <fin-icon src="assets/fabs/svgNeoGptFab.svg"></fin-icon>
          </button>
        </div>
      </div>
    `,
  }),
};
