import figma, { html } from '@figma/code-connect/html';

figma.connect(
  'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/branch/dfIfMwhNBkHjtNJaj8UcQ3/neodesign-System-v.2?node-id=38899-3905',
  {
    props: {},
    example: () =>
      html`<fin-zoom-bar
        [formControl]="formControl"
        orientation="horizontal"
        [min]="0"
        [max]="100"
        [step]="1"
      ></fin-zoom-bar>`,
    imports: ["import { FinZoomBarModule } from '@fincloud/ui/zoom-bar'"],
  },
);
