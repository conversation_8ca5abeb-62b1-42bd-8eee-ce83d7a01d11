import figma, { html } from '@figma/code-connect/html';

figma.connect(
  'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=6212-940',
  {
    props: {},
    example: () =>
      html`<fin-side-panel
        [open]="open"
        [partiallyOpenSize]="partiallyOpenSize"
        [mode]="mode"
        [position]="position"
        [elevation]="elevation"
        [hasBackdrop]="hasBackdrop"
        [transparentBackdrop]="transparentBackdrop"
        [disableClose]="disableClose"
        [fixedInViewport]="fixedInViewport"
      >
        <ng-template finSidePanelSlide>Slidable content</ng-template>

        <ng-template finSidePanelContent>Content</ng-template>
      </fin-side-panel>`,
    imports: ["import { FinSidePanelModule } from '@fincloud/ui/side-panel'"],
  },
);
