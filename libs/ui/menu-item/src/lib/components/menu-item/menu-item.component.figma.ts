import figma, { html } from '@figma/code-connect/html';

figma.connect(
  'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=14701-7894',
  {
    props: {},
    example: () =>
      html`<button
        fin-menu-item
        [size]="size"
        [compact]="compact"
        [disabled]="disabled"
        [attention]="attention"
        [active]="active"
        [iconName]="iconName"
        [iconSrc]="iconSrc"
      >
        <ng-container finMenuItemTitle>Title</ng-container>

        <ng-container finMenuItemDescription>Description</ng-container>

        <ng-container finMenuItemSuffix>
          <button fin-button-icon size="xs" appearance="informative">
            <fin-icon name="close"></fin-icon>
          </button>
        </ng-container>
      </button>`,
    imports: ["import { FinMenuItemModule } from '@fincloud/ui/menu-item'"],
  },
);
