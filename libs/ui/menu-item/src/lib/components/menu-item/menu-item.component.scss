:host {
  @apply fin-text-color-text-primary;
  @apply fin-bg-color-background-light;
  @apply fin-flex;
  @apply fin-items-center;
  @apply fin-rounded-[0.8rem];
  @apply fin-cursor-pointer;
  @apply fin-w-full;

  &:disabled {
    @apply fin-text-color-text-disabled;
    cursor: not-allowed;
    .fin-menu-item-description {
      @apply fin-text-color-text-disabled #{!important};
    }
  }

  &:disabled.fin-menu-item-attention {
    @apply fin-text-color-background-attention-moderate;
    .fin-menu-item-description {
      @apply fin-text-color-background-attention-moderate #{!important};
    }
  }

  &:hover:not(:disabled) {
    @apply fin-bg-color-background-tertiary-subtle;
  }

  &.fin-menu-item {
    &.fin-menu-item-l {
      @apply fin-px-[1.6rem];
      @apply fin-py-[0.8rem];
      @apply fin-gap-[2.4rem];
      @apply fin-min-h-[5.6rem];
    }

    &.fin-menu-item-m {
      @apply fin-p-[0.8rem];
      @apply fin-gap-[0.8rem];
      @apply fin-min-h-[4rem];
    }

    &-compact {
      justify-content: center;
    }

    &-active {
      @apply fin-bg-color-background-tertiary-minimal;
      @apply hover:fin-bg-color-background-tertiary-subtle;
    }

    &-attention {
      @apply fin-text-color-text-error;
      .fin-menu-item-description {
        @apply fin-text-color-text-error;
      }
    }
  }
}
