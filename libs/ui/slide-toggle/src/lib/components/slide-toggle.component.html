@if (control) {
  <mat-slide-toggle
    [formControl]="control"
    [labelPosition]="labelPosition"
    [ngClass]="['fin-toggle-' + size, 'fin-toggle-' + type, class]"
    (change)="onChange($event)"
  >
    @if (label) {
      <ng-container
        *ngTemplateOutlet="labelTemplate; context: { $implicit: label }"
      ></ng-container>
    }
  </mat-slide-toggle>
} @else {
  <mat-slide-toggle
    [labelPosition]="labelPosition"
    [ngClass]="['fin-toggle-' + size, 'fin-toggle-' + type, class]"
    [checked]="checked"
    (change)="onChange($event)"
    [disabled]="disabled"
  >
    @if (label) {
      <ng-container
        *ngTemplateOutlet="labelTemplate; context: { $implicit: label }"
      ></ng-container>
    }
  </mat-slide-toggle>
}

<ng-template #labelTemplate let-label>
  <mat-label
    class="fin-font-medium"
    [ngClass]="{
      'fin-me-[0.8rem]': labelPosition === positions.BEFORE,
      'fin-ms-[0.8rem]': labelPosition === positions.AFTER,
    }"
  >
    {{ label }}
  </mat-label>
</ng-template>
