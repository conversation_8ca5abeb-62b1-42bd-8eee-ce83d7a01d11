// Define color variables based on the theme function
$color-background-primary-strong: theme(
  'colors.color-background-primary-strong'
);
$color-hover-primary: theme('colors.color-hover-primary');
$color-background-neutral-minimal: theme(
  'colors.color-background-neutral-minimal'
);
$color-background-dark-moderate: theme(
  'colors.color-background-dark-moderate'
);
$color-background-dark-bold: theme('colors.color-background-dark-bold');
$color-border-default-inactive: theme('colors.color-border-default-inactive');
$color-background-disabled: theme('colors.color-background-disabled');
$color-background-secondary-strong: theme(
  'colors.color-background-secondary-strong'
);
$color-hover-secondary: theme('colors.color-hover-secondary');

:host {
  ::ng-deep {
    .mat-mdc-slide-toggle {
      .mdc-label {
        padding: 0;
      }

      .mdc-switch__icons,
      .mdc-switch__ripple {
        display: none;
      }

      .mdc-switch__shadow {
        display: none;
      }
    }
  }
}

@mixin handle-colors($selected-handle-color, $unselected-handle-color) {
  --mdc-switch-selected-handle-color: #{$selected-handle-color};
  --mdc-switch-selected-pressed-handle-color: #{$selected-handle-color};
  --mdc-switch-selected-hover-handle-color: #{$selected-handle-color};
  --mdc-switch-selected-focus-handle-color: #{$selected-handle-color};

  --mdc-switch-unselected-handle-color: #{$unselected-handle-color};
  --mdc-switch-unselected-pressed-handle-color: #{$unselected-handle-color};
  --mdc-switch-unselected-hover-handle-color: #{$unselected-handle-color};
  --mdc-switch-unselected-focus-handle-color: #{$unselected-handle-color};

  --mdc-switch-disabled-selected-handle-color: $color-background-disabled;
  --mdc-switch-disabled-unselected-handle-color: $color-background-disabled;
}

@mixin track-colors(
  $selected-track-color,
  $unselected-track-color,
  $hover-selected-track-color,
  $hover-unselected-track-color
) {
  --mdc-switch-selected-track-color: #{$selected-track-color};
  --mdc-switch-selected-focus-track-color: #{$selected-track-color};
  --mdc-switch-selected-pressed-state-layer-color: #{$selected-track-color};
  --mdc-switch-selected-focus-state-layer-color: #{$selected-track-color};
  --mdc-switch-selected-pressed-track-color: #{$selected-track-color};
  --mdc-switch-selected-hover-state-layer-color: #{$hover-selected-track-color};
  --mdc-switch-selected-hover-track-color: #{$hover-selected-track-color};

  --mdc-switch-unselected-track-color: #{$unselected-track-color};
  --mdc-switch-unselected-focus-track-color: #{$unselected-track-color};
  --mdc-switch-unselected-pressed-track-color: #{$unselected-track-color};
  --mdc-switch-unselected-hover-state-layer-color: #{$hover-unselected-track-color};
  --mdc-switch-unselected-hover-track-color: #{$hover-unselected-track-color};
}

@mixin handle-after-border(
  $selected-border,
  $unselected-border,
  $hover-selected-border,
  $hover-unselected-border,
  $disabled-border
) {
  ::ng-deep {
    .mdc-switch--selected .mdc-switch__handle::after {
      border: 0.1rem solid #{$selected-border};
    }
    .mdc-switch--selected:hover .mdc-switch__handle::after {
      border: 0.1rem solid #{$hover-selected-border};
    }
    .mdc-switch--unselected .mdc-switch__handle::after {
      border: 0.1rem solid #{$unselected-border};
    }
    .mdc-switch--unselected:hover .mdc-switch__handle::after {
      border: 0.1rem solid #{$hover-unselected-border};
    }
    .mdc-switch--disabled .mdc-switch__handle::after {
      border: 0.1rem solid #{$disabled-border};
    }
    .mdc-switch--disabled:hover .mdc-switch__handle::after {
      border: 0.1rem solid #{$disabled-border};
    }
  }
}

@mixin set-size(
  $mat-switch-width,
  $with-icon-handle-size,
  $unselected-handle-size,
  $selected-handle-size,
  $pressed-handle-size,
  $track-height,
  $track-width
) {
  ::ng-deep .mdc-switch {
    width: #{$mat-switch-width};

    --mat-switch-with-icon-handle-size: #{$with-icon-handle-size};
    --mat-switch-unselected-handle-size: #{$unselected-handle-size};
    --mat-switch-selected-handle-size: #{$selected-handle-size};
    --mat-switch-pressed-handle-size: #{$pressed-handle-size};

    --mdc-switch-track-height: #{$track-height};

    &__handle-track {
      width: #{$track-width};
    }
  }
}

mat-slide-toggle {
  --mdc-switch-disabled-label-text-color: theme('colors.color-text-disabled');
  --mdc-switch-handle-surface-color: #{$color-background-disabled};

  --mat-switch-disabled-selected-handle-opacity: 1;
  --mat-switch-disabled-unselected-handle-opacity: 1;

  ::ng-deep .mdc-form-field {
    display: flex;
    justify-content: space-between;
    .mdc-label {
      margin: 0;
      @apply fin-text-body-2-moderate;
    }
  }

  &.fin-toggle-secondary {
    --mdc-form-field-label-text-color: theme('colors.color-text-light');

    @include handle-colors(
      $color-background-neutral-minimal,
      $color-background-neutral-minimal
    );
    @include track-colors(
      $color-background-primary-strong,
      $color-background-dark-moderate,
      $color-hover-primary,
      $color-background-dark-bold
    );
    @include handle-after-border(
      $color-background-primary-strong,
      $color-background-dark-moderate,
      $color-hover-primary,
      $color-background-dark-bold,
      $color-border-default-inactive
    );
  }

  &.fin-toggle-primary {
    --mdc-form-field-label-text-color: theme('colors.color-text-primary');

    @include handle-colors(
      $color-background-neutral-minimal,
      $color-background-neutral-minimal
    );
    @include track-colors(
      $color-background-secondary-strong,
      $color-background-dark-moderate,
      $color-hover-secondary,
      $color-background-dark-bold
    );
    @include handle-after-border(
      $color-background-secondary-strong,
      $color-background-dark-moderate,
      $color-hover-secondary,
      $color-background-dark-bold,
      $color-border-default-inactive
    );
  }

  &.fin-toggle-m {
    @include set-size(2.6rem, 1.6rem, 1.6rem, 1.6rem, 1.6rem, 1.2rem, 1.1rem);
  }

  &.fin-toggle-s {
    @include set-size(1.8rem, 1.1rem, 1.1rem, 1.1rem, 1.1rem, 0.8rem, 0.8rem);
  }
}
