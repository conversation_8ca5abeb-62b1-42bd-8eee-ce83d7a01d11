import figma, { html } from '@figma/code-connect/html';

figma.connect(
  'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=27182-109834',
  {
    props: {
      // size: figma.enum('Size', {
      //   M: FinSize.M,
      //   L: FinSize.L,
      // }),
      // label: figma.string('Label'),
      // placeholder: figma.string('Placeholder'),
      multiple: figma.boolean('Multiselect'),
      // showChips: figma.boolean('Show Chips'),
      // autocomplete: figma.boolean('Autocomplete'),
      readonly: figma.boolean('Read only'),
      // hideArrow: figma.boolean('Hide Arrow'),
      // aiEnabled: figma.boolean('AI Enabled'),
    },
    example: (props) =>
      html`<fin-dropdown label="Label" [options]="options"></fin-dropdown>`,
    imports: ["import { FinDropdownModule } from '@fincloud/ui/dropdown'"],
  },
);
