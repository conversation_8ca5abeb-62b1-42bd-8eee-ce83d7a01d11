import { CommonModule } from '@angular/common';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { FinAiSuggestionModule } from '@fincloud/ui/ai-suggestion';
import { FinAvatarModule } from '@fincloud/ui/avatar-default';
import { FinBadgesModule } from '@fincloud/ui/badges';
import { FinButtonModule } from '@fincloud/ui/button';
import { FinFieldMessageModule } from '@fincloud/ui/field-message';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinTooltipModule } from '@fincloud/ui/tooltip';
import { FinTruncateTextModule } from '@fincloud/ui/truncate-text';
import { FinSize } from '@fincloud/ui/types';
import {
  componentWrapperDecorator,
  moduleMetadata,
  type Meta,
  type StoryObj,
} from '@storybook/angular';
import { FinDropdownModule } from '../../dropdown.module';
import { FinWordExtractorPipe } from '../../pipes/fin-word-extractor.pipe';
import { FIN_CUSTOM_MESSAGES } from '../../utils/fin-custom-messages-token';
import { FinDropdownComponent } from './dropdown.component';

const meta: Meta<FinDropdownComponent> = {
  component: FinDropdownComponent,
  title: 'Fields/Dropdown',
  decorators: [
    moduleMetadata({
      imports: [
        CommonModule,
        FinIconModule,
        FinDropdownModule,
        ReactiveFormsModule,
        FinFieldMessageModule,
        FinTruncateTextModule,
        FinBadgesModule,
        FinAvatarModule,
        FinWordExtractorPipe,
        FinButtonModule,
        FinTooltipModule,
        FinAiSuggestionModule,
      ],
      providers: [
        {
          provide: FIN_CUSTOM_MESSAGES,
          useValue: {
            initialMessage: 'Enter text to search',
            noResultsMessage: 'No results found',
          },
        },
      ],
    }),
    componentWrapperDecorator(
      (story) => `<div class="fin-w-[30rem]">${story}</div>`,
    ),
  ],
  parameters: {
    docs: {
      description: {
        component:
          '`import { FinDropdownModule } from "@fincloud/ui/dropdown"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=9454-28096&m=dev',
    },
  },
};
export default meta;
type Story = StoryObj<
  FinDropdownComponent & {
    finFieldLabel: string;
    finFieldPrefix: string;
    finFieldSuffix: string;
    finFieldHint: string;
    finFieldError: string;
    finOptionPrefixDirective: string;
    finOptionSuffixDirective: string;
    finOptionLabelDirective: string;
    finChipPrefixDirective: string;
    finChipSuffixDirective: string;
    finInitialMessageDirective: string;
    finNoResultsMessageDirective: string;
    formControl: FormControl;
  }
>;

const disabledField = new FormControl({ value: '', disabled: true });
const invalidField = new FormControl('', Validators.minLength(300));
invalidField.markAsTouched();
const validField = new FormControl('', Validators.required);
validField.markAsTouched();
const formControlReadonly = new FormControl('');
const field = new FormControl('');

export const Primary: Story = {
  args: {
    label: 'Dropdown label',
    placeholder: 'Placeholder',
    options: [
      {
        label: 'apple',
        value: 'apple',
      },
      {
        label: 'pear',
        value: 'fruit-pear',
      },
      {
        label: 'plum',
        value: 'fruit-plum',
      },
    ],
    size: FinSize.M,
    multiple: false,
    showChips: false,
    autocomplete: false,
  },
  argTypes: {
    label: {
      control: { type: 'text' },
    },
    placeholder: {
      control: { type: 'text' },
    },
    size: {
      options: [FinSize.M, FinSize.L],
      control: { type: 'select' },
    },

    selectionChange: {
      control: false,
    },
    chipRemoved: {
      control: false,
    },
    autoCompleteInputChange: {
      control: false,
    },

    finFieldLabel: {
      description: 'Place the label with addition icons next to it',
      table: {
        category: 'Templates',
      },
    },
    finFieldPrefix: {
      description: 'Place icons before the dropdown',
      table: {
        category: 'Templates',
      },
    },
    finFieldSuffix: {
      description: 'Place icons after the dropdown',
      table: {
        category: 'Templates',
      },
    },
    finFieldHint: {
      description: 'Place the hint text',
      table: {
        category: 'Templates',
      },
    },
    finFieldError: {
      description: 'Place error message',
      table: {
        category: 'Templates',
      },
    },
    finOptionPrefixDirective: {
      description: 'Dropdown option prefix',
      table: {
        category: 'Templates',
      },
    },
    finOptionSuffixDirective: {
      description: 'Dropdown option suffix',
      table: {
        category: 'Templates',
      },
    },
    finOptionLabelDirective: {
      description: 'Dropdown option label',
      table: {
        category: 'Templates',
      },
    },
    finChipPrefixDirective: {
      description: 'Selected option chip prefix',
      table: {
        category: 'Templates',
      },
    },
    finChipSuffixDirective: {
      description: 'Selected option chip suffix',
      table: {
        category: 'Templates',
      },
    },
    finInitialMessageDirective: {
      description: 'Custom message to show initially before options are loaded',
      table: {
        category: 'Templates',
      },
    },
    finNoResultsMessageDirective: {
      description: 'Custom message to show when there are no results',
      table: {
        category: 'Templates',
      },
    },
  },
  render: (args) => {
    return {
      props: { ...args, formControl: new FormControl('') },
    };
  },
};

export const CustomLabel: Story = {
  argTypes: {},
  args: {
    ...Primary.args,
  },

  render: (args) => {
    return {
      props: {
        ...args,
        formControl: field,
        label: 'Long label with a lot of numbers of multiple actions',
      },
      template: `
        <fin-dropdown
          [formControl]="formControl"
          [options]="options"
        >
          <ng-container finFieldLabel>
            <label class="fin-inline-block fin-w-[24rem]" finTruncateText>
              {{ label }} 
            </label>            
            
            <div class="fin-flex fin-gap-[0.4rem]">
              <button fin-button-action finTooltip content="Open chat" (click)="clickFn()">
                <fin-icon src="/assets/storybook/input/chat-bubble.svg"></fin-icon>
              </button>

              <button fin-button-action (click)="clickFn()">
                <fin-icon src="/assets/storybook/input/info.svg"></fin-icon>
              </button>

              <button fin-button-action (click)="clickFn()">
                <fin-icon src="/assets/storybook/input/visibility-off.svg"></fin-icon>
              </button>
            </div>
          </ng-container>
        </fin-dropdown>
      `,
    };
  },
};

export const Hint: Story = {
  argTypes: {},
  args: {
    ...Primary.args,
  },

  render: (args) => {
    return {
      props: {
        ...args,
        formControl: field,
      },
      template: `
        <fin-dropdown
          [label]="label"
          [formControl]="formControl"
          [options]="options"
        >
          <ng-container finFieldHint>
            Some hint text
          </ng-container>
        </fin-dropdown>
      `,
    };
  },
};

export const Disabled: Story = {
  argTypes: {},
  args: {
    ...Primary.args,
  },
  render: (args) => {
    const formControl = new FormControl({ value: '', disabled: true });
    return {
      props: {
        ...args,
        formControl,
      },
      template: `
        <fin-dropdown
          [label]="label"
          [formControl]="formControl"
          [options]="options"
        >
        </fin-dropdown>
      `,
    };
  },
};

export const ReadonlyField: Story = {
  argTypes: {},
  args: {
    ...Primary.args,
  },
  render: (args) => {
    return {
      props: {
        ...args,
        formControl: formControlReadonly,
      },
      template: `
        <fin-dropdown
          [label]="label"
          [formControl]="formControl"
          [options]="options"
          [readonly]="true"
        ></fin-dropdown>
      `,
    };
  },
};

export const PrefixFields: Story = {
  name: 'Prefix fields',
  argTypes: {},
  args: {
    ...Primary.args,
  },
  render: (args) => {
    return {
      props: {
        ...args,
        formControl: field,
      },
      template: `
        <fin-dropdown
          [options]="options"
          [label]="label"
          [formControl]="formControl"
        >
          <fin-icon finFieldPrefix src="/assets/storybook/input/de-flag.svg"></fin-icon>
        </fin-dropdown>
      `,
    };
  },
};

export const SuffixFields: Story = {
  name: 'Suffix fields',
  argTypes: {},
  args: {
    ...Primary.args,
  },
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=6547-13078&m=dev',
    },
  },
  render: (args) => {
    return {
      props: {
        ...args,
        formControl: field,
        disabledField,
        clearFn: (event: any) => {
          event.stopPropagation();
          field.reset();
        },
      },
      template: `
        <fin-dropdown
          [options]="options"
          [label]="label"
          [formControl]="formControl"
        >
          <ng-container finFieldSuffix>
              <button type="button" class="fin-pl-[0.4rem]" (click)="clearFn($event);">
                <fin-icon src="/assets/storybook/input/more-horiz.svg"></fin-icon>
              </button>
          </ng-container>
        </fin-dropdown>
      `,
    };
  },
};

export const ValidationSuccess: Story = {
  argTypes: {},
  args: {
    ...Primary.args,
  },
  render: (args) => {
    const formControl = new FormControl('');
    formControl.markAsTouched();

    return {
      props: {
        ...args,
        formControl,
      },
      template: `
        <fin-dropdown
          [label]="label"
          [formControl]="formControl"
          [options]="options"
        >
          <fin-field-messages>
            <ng-template finFieldMessage type="success">
              Some success message.
            </ng-template>
          </fin-field-messages>          
        </fin-dropdown>
      `,
    };
  },
};

export const ValidationError: Story = {
  argTypes: {},
  args: {
    ...Primary.args,
  },
  render: (args) => {
    const formControl = new FormControl('', [Validators.required]);
    formControl.markAsTouched();

    return {
      props: {
        ...args,
        formControl,
      },
      template: `
        <fin-dropdown
          [label]="label"
          [formControl]="formControl"
          [options]="options"
        >
          <fin-field-messages>
            <ng-template finFieldMessage type="error" errorKey="required">
              Some error message.
            </ng-template>
          </fin-field-messages>          
        </fin-dropdown>
      `,
    };
  },
};

export const ValidationWarning: Story = {
  argTypes: {},
  args: {
    ...Primary.args,
  },
  render: (args) => {
    const formControl = new FormControl('', [Validators.requiredTrue]);
    formControl.markAsTouched();

    return {
      props: {
        ...args,
        formControl,
      },
      template: `
        <fin-dropdown
          [label]="label"
          [formControl]="formControl"
          [options]="options"
        >
          <fin-field-messages>
            <ng-template finFieldMessage type="warning" errorKey="required">
              Some warning message.
            </ng-template>
          </fin-field-messages>          
        </fin-dropdown>
      `,
    };
  },
};

export const Autocomplete: Story = {
  args: {
    ...Primary.args,
    autocomplete: true,
    options: [
      {
        label: 'apple',
        value: 'apple',
      },
      {
        label: 'pear',
        value: 'pear',
      },
      {
        label: 'plum',
        value: 'plum',
      },
    ],
  },
  argTypes: {
    ...Primary.argTypes,
  },
  render: (args) => {
    return {
      props: { ...args, formControl: new FormControl() },
      template: `
        <fin-dropdown
          label="Label"
          placeholder="Placeholder"
          [formControl]="formControl"
          [options]="options"
          [autocomplete]="autocomplete"
          dynamicErrorSpace="dynamic"
        >
        </fin-dropdown>
    `,
    };
  },
};

export const AutocompleteNoOptions: Story = {
  args: {
    ...Primary.args,
    autocomplete: true,
    options: [],
  },
  argTypes: {
    ...Primary.argTypes,
  },
  render: (args) => {
    return {
      props: { ...args, formControl: new FormControl() },
      template: `
        <fin-dropdown
          label="Label"
          placeholder="Placeholder"
          [formControl]="formControl"
          [options]="options"
          [autocomplete]="autocomplete"
          dynamicErrorSpace="dynamic"
        >
        </fin-dropdown>
    `,
    };
  },
};

export const CustomMessages: Story = {
  args: {
    ...Primary.args,
    autocomplete: true,
    options: [],
  },
  argTypes: {
    ...Primary.argTypes,
  },
  render: (args) => {
    return {
      props: { ...args, formControl: new FormControl() },
      template: `
        <fin-dropdown
          label="Label"
          placeholder="Placeholder"
          [formControl]="formControl"
          [options]="options"
          [autocomplete]="autocomplete"
          dynamicErrorSpace="dynamic"
        >
          <ng-container finInitialMessage>Custom initial message</ng-container>
          <ng-container finNoResultsMessage>Custom message when there are no results</ng-container>
        </fin-dropdown>
    `,
    };
  },
};

export const Multiselect: Story = {
  args: {
    ...Primary.args,
    multiple: true,
    options: [
      {
        label: 'Option 1',
        value: 'Option 1',
      },
      {
        label: 'Option 2',
        value: 'Option 2',
      },
      {
        label: 'Option 3',
        value: 'Option 3',
      },
      {
        label: 'Option 4',
        value: 'Option 4',
      },
      {
        label: 'Option 5',
        value: 'Option 5',
      },
      {
        label: 'Option 6',
        value: 'Option 6',
      },
    ],
  },
  argTypes: {
    ...Primary.argTypes,
  },
  render: (args) => {
    return {
      props: {
        ...args,
        formControl: new FormControl(),
      },
    };
  },
};

export const ChipsMode: Story = {
  args: {
    ...Primary.args,
    autocomplete: true,
    showChips: true,
    options: [
      {
        label: 'Max Lee',
        value: 'option1',
      },
      {
        label: 'Eva Kim',
        value: 'option2',
      },
      {
        label: 'Ben Wu',
        value: 'option3',
      },
      {
        label: 'Victoria Henderson',
        value: 'option4',
      },
      {
        label: 'Nathaniel Rodriguez',
        value: 'option5',
      },
      {
        label: 'Alexander Thompson',
        value: 'option6',
      },
      {
        label: 'Christopher Nathaniel Montgomery',
        value: 'option7',
      },
      {
        label: 'Christopher Nathaniel Montgomery-Winchester',
        value: 'option8',
      },
      {
        label: 'Jonathan Alexander Whitmore-Harrington',
        value: 'option9',
      },
    ],
  },
  argTypes: {
    ...Primary.argTypes,
  },
  render: (args) => {
    return {
      props: {
        ...args,
        formControl: new FormControl(['option1', 'option2', 'option9']),
      },
      template: `
        <fin-dropdown
          label="Label"
          placeholder="Placeholder"
          [formControl]="formControl"
          [options]="options"
          [autocomplete]="autocomplete"
          [showChips]="showChips"
          dynamicErrorSpace="dynamic"
        >
        </fin-dropdown>
    `,
    };
  },
};

export const ChipsModeWithAvatar: Story = {
  args: {
    ...Primary.args,
    autocomplete: true,
    showChips: true,
    options: ChipsMode.args?.options,
  },
  argTypes: {
    ...Primary.argTypes,
  },
  render: (args) => {
    return {
      props: {
        ...args,
        formControl: new FormControl(['option1', 'option2', 'option9']),
      },
      template: `
        <fin-dropdown
          label="Label"
          placeholder="Placeholder"
          [formControl]="formControl"
          [options]="options"
          [autocomplete]="autocomplete"
          [showChips]="showChips"
          dynamicErrorSpace="dynamic"
        >
        <ng-template [finChipPrefix]="options" let-option>
          <fin-avatar-default
            [firstName]="option.label | finWordExtractor: 'first'"
            [lastName]="option.label | finWordExtractor: 'last'"
            [size]="'xs'"
          ></fin-avatar-default>
        </ng-template>
        </fin-dropdown>
    `,
    };
  },
};

export const AiSuggestion: Story = {
  args: {
    ...Primary.args,
    autocomplete: true,
    options: [
      { label: 'Berlin', value: 'berlin' },
      { label: 'Munich', value: 'munich' },
      { label: 'Hamburg', value: 'hamburg' },
    ],
  },
  argTypes: {
    ...Primary.argTypes,
  },
  render: (args) => {
    const formControl = new FormControl('');
    const form = new FormGroup({ formControl });
    return {
      props: {
        ...args,
        form,
        formControl,
        suggestBerlin: () => formControl.setValue('Berlin'),
        suggestMunich: () => formControl.setValue('Munich'),
      },
      template: `
        <form [formGroup]="form">
          <fin-dropdown
            label="City with AI suggestion"
            placeholder="Type to search"
            formControlName="formControl"
            [options]="options"
            [autocomplete]="true"
            dynamicErrorSpace="dynamic"
          >
            <fin-ai-suggestion enabled formControlName="formControl"></fin-ai-suggestion>
          </fin-dropdown>

          <div class="fin-flex fin-gap-[0.8rem] fin-mt-6">
            <button fin-button size="m" shape="round" appearance="stealth" (click)="suggestBerlin()">
              <fin-icon src="assets/storybook/icons/ai.svg"></fin-icon>
              Suggest Berlin
            </button>
            <button fin-button size="m" shape="round" appearance="stealth" (click)="suggestMunich()">
              <fin-icon src="assets/storybook/icons/ai.svg"></fin-icon>
              Suggest Munich
            </button>
          </div>
        </form>
      `,
    };
  },
};

export const CustomOption: Story = {
  argTypes: {},
  args: {
    ...Primary.args,
  },

  render: (args) => {
    return {
      props: {
        ...args,
        formControl: field,
        label: 'Long label with multiple actions',
      },
      template: `
        <fin-dropdown [formControl]="formControl" [options]="options" label="Custom option">
          <ng-template [finOptionPrefix]="options" let-option>
            <fin-badge-invitation [type]="'regular'"></fin-badge-invitation>
          </ng-template>
          <ng-template [finOptionSuffix]="options" let-option>
            <fin-badge-invitation [type]="'unregistered-guest'"></fin-badge-invitation>
          </ng-template>
        </fin-dropdown>
      `,
    };
  },
};
