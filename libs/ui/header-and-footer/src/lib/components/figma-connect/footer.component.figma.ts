import figma, { html } from '@figma/code-connect/html';
import { FinSize } from '@fincloud/ui/types';

figma.connect(
  'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=22333-57115',
  {
    props: {
      size: figma.enum('Size', {
        S: FinSize.S,
        M: FinSize.M,
      }),
    },
    example: (props) =>
      html`<fin-footer [size]="${props.size}">
        <button fin-button appearance="secondary">Close</button>

        <button fin-button appearance="primary">Action</button>
      </fin-footer>`,
    imports: [
      "import { FinHeaderAndFooterModule } from '@fincloud/ui/header-and-footer'",
    ],
  },
);
