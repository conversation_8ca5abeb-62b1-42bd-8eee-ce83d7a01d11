import figma, { html } from '@figma/code-connect/html';
import { FinSize } from '@fincloud/ui/types';

figma.connect(
  'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=20781-164809',
  {
    props: {
      size: figma.enum('Size', {
        S: FinSize.S,
        M: FinSize.M,
      }),
    },
    example: (props) =>
      html`<fin-header [size]="${props.size}">
        Header

        <button fin-button-action size="l">
          <fin-icon name="close"></fin-icon>
        </button>
      </fin-header>`,
    imports: [
      "import { FinHeaderAndFooterModule } from '@fincloud/ui/header-and-footer'",
    ],
  },
);
