import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinTruncateTextDirective } from '@fincloud/ui/truncate-text';
import { FinSize } from '@fincloud/ui/types';

@Component({
  selector: 'fin-asset-card-item',
  standalone: true,
  imports: [CommonModule, FinIconModule, FinTruncateTextDirective],
  templateUrl: './asset-card-item.component.html',
  styleUrl: './asset-card-item.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    class: 'fin-asset-card-item',
  },
})
export class FinAssetCardItemComponent {
  /** Name of an icon within a [Material Icons](https://fonts.google.com/icons) font set. */
  @Input() icon = '';

  /** Defined description. */
  @Input({ required: true }) description = '';

  protected size = FinSize;
}
