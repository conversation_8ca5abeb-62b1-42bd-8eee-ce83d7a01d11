import { CommonModule } from '@angular/common';
import { FinBadgesModule } from '@fincloud/ui/badges';
import { FinButtonModule } from '@fincloud/ui/button';
import { FinIconModule } from '@fincloud/ui/icon';
import { moduleMetadata, type Meta, type StoryObj } from '@storybook/angular';
import { FinAssetCardModule } from '../../asset-card.module';
import { FinAssetCardComponent } from './asset-card.component';

const meta: Meta = {
  component: FinAssetCardComponent,
  title: 'Components/Asset card',
  parameters: {
    docs: {
      description: {
        component:
          '`import { FinAssetCardModule } from "@fincloud/ui/asset-card"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/sBb3FhaE0tuWi2u1OQOu1I/FINUI-669-%7C-DS-7-%7C-SIGMA-13550--%3E--AM--Tiles?node-id=41907-131303&m=dev',
    },
  },
  decorators: [
    moduleMetadata({
      imports: [
        CommonModule,
        FinAssetCardModule,
        FinBadgesModule,
        FinButtonModule,
        FinIconModule,
      ],
    }),
  ],
};
export default meta;

type Story = StoryObj<
  FinAssetCardComponent & {
    finAssetCardHeader: string;
    finAssetCardItemContainer: string;
    icon: string;
    'description ': string;
  }
>;

const assetCardItems = [
  {
    icon: 'abc',
    description:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod',
  },
  {
    icon: 'account_box',
    description:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod',
  },
  {
    icon: 'sell',
    description:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod',
  },
];
const fullExampleTemplate = `
    <fin-asset-card
      [label]="label"
      [description]="description"
      [backgroundImage]="backgroundImage"
      [opacityBackground]="opacityBackground"
    >
      <ng-template #finAssetCardHeader>
        <div class="fin-h-full fin-flex fin-flex-col">
          <div class="fin-flex fin-justify-end fin-p-size-spacing-16">
            <button fin-button-icon appearance="informative" shape="round">
              <fin-icon name="more_vert"></fin-icon>
            </button>
          </div>

          <div class="fin-flex-grow fin-flex fin-items-center fin-justify-center">
            <fin-badge-status
              iconName="archive"
              type="Draft"
              text="Archived"
              size="m"
            ></fin-badge-status>
          </div>

          <div class="fin-p-size-spacing-8">
            <fin-badge-status type="InProgress" text="Status"></fin-badge-status>
          </div>
        </div>
      </ng-template>

      <ng-template #finAssetCardItemContainer>
        @for (assetCardItem of assetCardItems; track $index) {
          <fin-asset-card-item
            [icon]="assetCardItem.icon"
            [description]="assetCardItem.description"
          ></fin-asset-card-item>
        }
      </ng-template>
    </fin-asset-card>
  `;

export const Primary: Story = {
  args: {
    label: 'Skalitzer Straße 141, Berlin, 10999, Berlin, Germany',
    description:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incidid.',
    backgroundImage: 'assets/storybook/asset-card/image.png',
    opacityBackground: false,
  },
  argTypes: {
    // Asset card inputs
    label: {
      description: 'Defined label.',
      table: {
        category: 'Asset card inputs',
      },
    },
    description: {
      description: 'Defined description.',
      table: {
        category: 'Asset card inputs',
      },
    },
    backgroundImage: {
      description: 'Background image URL or path.',
      table: {
        category: 'Asset card inputs',
      },
    },
    opacityBackground: {
      description: 'Whether the background image should have opacity.',
      table: {
        category: 'Asset card inputs',
      },
    },

    // Asset card item inputs
    icon: {
      description:
        'Name of an icon within a [Material Icons](https://fonts.google.com/icons) font set.',
      control: false,
      defaultValue: { summary: '' },
      table: {
        category: 'Asset card item inputs',
      },
    },
    'description ': {
      description: 'Defined description.',
      control: false,
      defaultValue: { summary: '' },
      table: {
        category: 'Asset card item inputs',
      },
    },

    // Templates
    finAssetCardHeader: {
      description: 'Template for the header area',
      control: false,
      table: {
        category: 'Templates',
      },
    },
    finAssetCardItemContainer: {
      description: 'Template for the content area.',
      control: false,
      table: {
        category: 'Templates',
      },
    },
  },
  render: (args) => ({
    props: { ...args, assetCardItems },
    template: `
      <fin-asset-card
        [label]="label"
        [description]="description"
        [backgroundImage]="backgroundImage"
        [opacityBackground]="opacityBackground"
      >       
        <ng-template #finAssetCardItemContainer>
          @for (assetCardItem of assetCardItems; track $index) {
            <fin-asset-card-item
              [icon]="assetCardItem.icon"
              [description]="assetCardItem.description"
            ></fin-asset-card-item>
          }
        </ng-template>
      </fin-asset-card>
    `,
  }),
};

export const HeaderTemplate: Story = {
  args: {
    ...Primary.args,
  },
  argTypes: {
    ...Primary.argTypes,
  },
  render: (args) => ({
    props: { ...args, assetCardItems },
    template: fullExampleTemplate,
  }),
};

export const BackgroundOpacity: Story = {
  args: {
    ...Primary.args,
    opacityBackground: true,
  },
  argTypes: {
    ...Primary.argTypes,
  },
  render: (args) => ({
    props: { ...args, assetCardItems },
    template: fullExampleTemplate,
  }),
};
