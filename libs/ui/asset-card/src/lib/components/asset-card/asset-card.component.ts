import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  ContentChild,
  Input,
  TemplateRef,
} from '@angular/core';
import { FinTruncateTextModule } from '@fincloud/ui/truncate-text';

/**
 * A card component for displaying asset information.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-asset-card--docs Storybook Reference}
 */
@Component({
  selector: 'fin-asset-card',
  standalone: true,
  imports: [CommonModule, FinTruncateTextModule],
  templateUrl: './asset-card.component.html',
  styleUrl: './asset-card.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    class: 'fin-asset-card',
  },
})
export class FinAssetCardComponent {
  /** Defined label. */
  @Input() label = '';

  /** Defined description. */
  @Input() description = '';

  /** Background image URL or path. */
  @Input() backgroundImage = '';

  /** Whether the background image should have opacity. */
  @Input() opacityBackground = false;

  @ContentChild('finAssetCardHeader')
  protected assetCardHeaderTemplate?: TemplateRef<unknown>;

  @ContentChild('finAssetCardItemContainer')
  protected assetCardItemContainerTemplate?: TemplateRef<unknown>;
}
