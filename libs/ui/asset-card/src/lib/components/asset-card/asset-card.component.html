<div class="fin-group">
  @if (assetCardHeaderTemplate || backgroundImage) {
    <div
      class="fin-overflow-hidden"
      [ngStyle]="{
        'background-image': 'url(' + backgroundImage + ')',
        'background-size': 'cover',
        'background-position': 'center',
      }"
      [ngClass]="{
        'fin-aspect-[3/2]': backgroundImage,
        'fin-bg-white/60 fin-bg-blend-lighten':
          backgroundImage && opacityBackground,
      }"
    >
      @if (assetCardHeaderTemplate) {
        <ng-container
          [ngTemplateOutlet]="assetCardHeaderTemplate"
        ></ng-container>
      }
    </div>
  }

  <div class="group-hover:fin-bg-color-hover-neutral fin-p-size-spacing-16">
    @if (label) {
      <div
        finTruncateText
        class="fin-text-color-text-primary fin-text-body-1-strong"
      >
        {{ label }}
      </div>
    }

    @if (description) {
      <div
        finTruncateText
        class="fin-text-color-text-secondary fin-text-body-3-moderate"
        [class.fin-mt-size-spacing-2]="label"
      >
        {{ description }}
      </div>
    }

    @if (assetCardItemContainerTemplate) {
      <div
        class="fin-flex fin-flex-col fin-gap-size-spacing-8 fin-mt-size-spacing-16"
      >
        <ng-container
          [ngTemplateOutlet]="assetCardItemContainerTemplate"
        ></ng-container>
      </div>
    }
  </div>
</div>
