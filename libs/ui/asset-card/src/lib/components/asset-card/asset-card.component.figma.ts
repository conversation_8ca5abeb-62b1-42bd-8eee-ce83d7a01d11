import figma, { html } from '@figma/code-connect/html';

figma.connect(
  'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/branch/sBb3FhaE0tuWi2u1OQOu1I/neodesign-System-v.2?node-id=41903-130851',
  {
    props: {},
    example: () =>
      html` <fin-asset-card
        [label]="label"
        [description]="description"
        [backgroundImage]="backgroundImage"
        [opacityBackground]="opacityBackground"
      >
        <ng-template #finAssetCardHeader>Header template</ng-template>

        <ng-template #finAssetCardItemContainer>
          @for (assetCardItem of assetCardItems; track $index) {
          <fin-asset-card-item
            [icon]="assetCardItem.icon"
            [description]="assetCardItem.description"
          ></fin-asset-card-item>
          }
        </ng-template>
      </fin-asset-card>`,
    imports: ["import { FinAssetCardModule } from '@fincloud/ui/asset-card'"],
  },
);
