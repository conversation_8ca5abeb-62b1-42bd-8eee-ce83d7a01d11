import figma, { html } from '@figma/code-connect/html';

figma.connect(
  'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=24558-20782',
  {
    props: {},
    example: () =>
      html`<div finDropList [finDropListData]="items">
        @for (item of items; track $index) {
          <div fin-drag-drop-item finDrag>
            <fin-icon name="local_grocery_store" size="s"></fin-icon>

            <div>{{ item }}</div>

            <fin-icon name="drag_indicator" size="s"></fin-icon>
          </div>
        }
      </div>`,
    imports: ["import { FinDragDropModule } from '@fincloud/ui/drag-drop'"],
  },
);
