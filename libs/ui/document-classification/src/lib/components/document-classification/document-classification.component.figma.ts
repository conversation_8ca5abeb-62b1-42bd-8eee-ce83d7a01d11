import figma, { html } from '@figma/code-connect/html';

figma.connect(
  'https://www.figma.com/design/QdgzuRY5ZluGdKBU4NQRy9/Inbox-2.0?node-id=3459-427603&',
  {
    props: {},
    example: () =>
      html` <fin-document-classification
        [placeholder]="placeholder"
        [readonly]="readonly"
        [expanded]="expanded"
        [disabled]="disabled"
      >
        <ng-template #finPrefix>Prefix</ng-template>

        <ng-template #finInputPrefix>Input prefix</ng-template>

        <ng-template #finContent>Content</ng-template>
      </fin-document-classification>`,
    imports: [
      "import { FinDocumentClassificationModule } from '@fincloud/ui/document-classification'",
    ],
  },
);
