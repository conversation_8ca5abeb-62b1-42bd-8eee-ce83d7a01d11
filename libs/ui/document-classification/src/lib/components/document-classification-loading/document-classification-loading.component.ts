import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  Component,
  ContentChild,
  DestroyRef,
  ElementRef,
  EventEmitter,
  Input,
  OnChanges,
  Output,
  QueryList,
  SimpleChanges,
  TemplateRef,
  ViewChildren,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FinAnimationsModule } from '@fincloud/ui/animations';
import { FinButtonAppearance, FinButtonModule } from '@fincloud/ui/button';
import { FinTooltipModule } from '@fincloud/ui/tooltip';
import { FinTruncateTextModule } from '@fincloud/ui/truncate-text';
import { FinSize } from '@fincloud/ui/types';
import { pxToRem } from '@fincloud/utils/functions';
import { animationFrameScheduler, observeOn } from 'rxjs';
import { DocumentClassificationState } from '../../models/document-classification-state';
import { FinGetFileExtensionPipe } from '../../pipes/get-file-extension.pipe';
import { FinGetFileNamePipe } from '../../pipes/get-file-name.pipe';

@Component({
  selector: 'fin-document-classification-loading',
  standalone: true,
  imports: [
    CommonModule,
    FinAnimationsModule,
    FinGetFileExtensionPipe,
    FinGetFileNamePipe,
    FinTruncateTextModule,
    FinButtonModule,
    FinTooltipModule,
  ],
  templateUrl: './document-classification-loading.component.html',
  styleUrl: './document-classification-loading.component.scss',
  // changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinDocumentClassificationLoadingComponent
  implements AfterViewInit, OnChanges
{
  @Input() state!: DocumentClassificationState;
  @Input() controlValue!: string;
  @Input() btnCancelLabel!: string;
  @Input() maxWidth!: number;

  @Output() canceledLoading = new EventEmitter<Event>();
  @Output() updateState = new EventEmitter<
    Partial<DocumentClassificationState>
  >();

  @ContentChild('finInputPrefix')
  protected inputPrefixTemplate?: TemplateRef<unknown>;

  @ViewChildren('fileName')
  private fileName!: QueryList<ElementRef<HTMLDivElement>>;

  @ViewChildren('loadingMessage')
  private loadingMessage!: QueryList<ElementRef<HTMLDivElement>>;

  @ViewChildren('timeoutMessage')
  private timeoutMessage!: QueryList<ElementRef<HTMLDivElement>>;

  @ViewChildren('loadingCancelBtn', { read: ElementRef })
  private loadingCancelBtn!: QueryList<ElementRef<HTMLButtonElement>>;

  @ViewChildren('timeoutMessagePartElem', { read: ElementRef })
  private timeoutMessagePartElem!: QueryList<ElementRef<HTMLDivElement>>;

  protected finButtonAppearance = FinButtonAppearance;
  protected finButtonSize = FinSize;
  private loadingCmpWidth = 0;
  private paddingWidth = 45;

  constructor(
    private destroyRef: DestroyRef,
    private elementRef: ElementRef,
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (this.loadingMessage) {
      this.setupFileNameTruncation();
    }
    if (changes['state']?.firstChange) {
      this.loadingCmpWidth =
        this.elementRef.nativeElement.getBoundingClientRect().width;
    }
  }

  ngAfterViewInit(): void {
    this.setupFileNameTruncation();
  }

  /** Cancels the loading workflow and notifies parent components. */
  protected onCancelLoading(event: Event) {
    event.stopPropagation();
    this.canceledLoading.emit(event);
  }

  private setupFileNameTruncation() {
    const loadingMessage = this.loadingMessage;
    const loadingMessageWidth =
      loadingMessage.first?.nativeElement.getBoundingClientRect().width;

    const loadingMessageFileNameWidth =
      this.fileName.first?.nativeElement.getBoundingClientRect().width;

    if (
      this.loadingCmpWidth > 0 &&
      loadingMessageWidth > this.loadingCmpWidth
    ) {
      this.updateState.emit({
        loadingFileNameWidth: pxToRem(
          this.loadingCmpWidth -
            (loadingMessageWidth - loadingMessageFileNameWidth) -
            this.paddingWidth,
        ),
      });
    }

    this.timeoutMessage.changes
      .pipe(
        observeOn(animationFrameScheduler),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe((data) => {
        const timeOutMessageElem = data as QueryList<
          ElementRef<HTMLDivElement>
        >;
        const timeOutMessageWidth =
          timeOutMessageElem.first?.nativeElement.getBoundingClientRect().width;

        const loadingCancelBtnWidth =
          this.loadingCancelBtn.first?.nativeElement.getBoundingClientRect()
            .width;

        const timeoutMessagePartsWidth = this.timeoutMessagePartElem.reduce(
          (totalWidth, messagePart) =>
            totalWidth +
            messagePart.nativeElement.getBoundingClientRect().width,
          0,
        );

        if (
          loadingCancelBtnWidth + timeOutMessageWidth + this.paddingWidth >
          this.loadingCmpWidth
        ) {
          this.updateState.emit({
            loadingMessageWithFileNameWidth: pxToRem(
              this.loadingCmpWidth -
                (timeoutMessagePartsWidth +
                  loadingCancelBtnWidth +
                  this.paddingWidth),
            ),
          });
        }
      });
  }
}
