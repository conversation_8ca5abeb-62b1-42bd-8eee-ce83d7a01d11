import figma, { html } from '@figma/code-connect/html';

figma.connect(
  'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=25971-77179',
  {
    props: {},
    example: () =>
      html`<fin-toolbar>
        <fin-tabs type="secondary">
          <fin-tab>
            <ng-template finTabLabel>Tab 1</ng-template>
          </fin-tab>

          <fin-tab>
            <ng-template finTabLabel>Tab 2</ng-template>
          </fin-tab>
        </fin-tabs>

        <ng-container finToolbarSuffix>
          <fin-slide-toggle
            [formControl]="formControl"
            label="Edit mode"
          ></fin-slide-toggle>
        </ng-container>
      </fin-toolbar>`,
    imports: ["import { FinToolbarModule } from '@fincloud/ui/toolbar'"],
  },
);
