import { EventEmitter } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { FinObserversModule } from '../observers.module';
const meta: Meta = {
  title: 'Directives/Resize Observer',
  parameters: {
    docs: {
      description: {
        component:
          '`import { FinObserversModule } from "@fincloud/ui/observers"`',
      },
    },
  },
  decorators: [
    moduleMetadata({
      imports: [BrowserModule, FinObserversModule],
    }),
  ],
  argTypes: {
    finObserveResize: {
      control: false,
      description:
        "Event emitted for each change in the element's content.<br>`EventEmitter<ResizeObserverEntry>`",
      table: {
        category: 'Outputs',
      },
    },
    finObserveResizeDebounce: {
      control: 'number',
      description: 'Debounce interval for emitting the changes.',
      table: {
        category: 'Inputs',
      },
    },
    finObserveResizeDisabled: {
      control: 'boolean',
      description:
        'Whether observing size is disabled. This option can be used to disconnect the underlying ResizeObserver until it is needed.',
      table: {
        category: 'Inputs',
      },
    },
  },
  args: {
    finObserveResizeDebounce: 0,
    finObserveResizeDisabled: false,
  },
};

export default meta;
type Story = StoryObj<{
  finObserveResize: EventEmitter<ResizeObserverEntry>;
}>;

export const Example: Story = {
  render: (args) => ({
    props: {
      ...args,
      onResized: (entry: ResizeObserverEntry) => {
        console.log(entry);
      },
    },
    template: `
        <div
          class="fin-w-[30vw] fin-mx-4 fin-p-8 fin-border-2 fin-text-xl fin-font-medium"
          (finObserveResize)="onResized($event)"
          [finObserveResizeDebounce]="finObserveResizeDebounce"
          [finObserveResizeDisabled]="finObserveResizeDisabled"
         >
           Open the console and start resizing the window.
        </div>
     `,
  }),
};
