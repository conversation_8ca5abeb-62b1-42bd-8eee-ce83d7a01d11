import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  booleanAttribute,
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  EventEmitter,
  forwardRef,
  Injector,
  Input,
  Optional,
  Output,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { NG_VALUE_ACCESSOR, ReactiveFormsModule } from '@angular/forms';
import {
  FinFieldMessageBodyDirective,
  FinFieldMessageService,
} from '@fincloud/ui/field-message';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinSize } from '@fincloud/ui/types';
import { FinAngularMaterialModule } from '@fincloud/utils/angular-material';
import { FinControlValueAccessor } from '@fincloud/utils/control-value-accessor';
import { FinFieldService } from '@fincloud/utils/services';
import { shareReplay, startWith } from 'rxjs';

/**
 * A numeric stepper field that allows users to input a numerical value with increment and decrement controls for easy adjustment.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-stepper-field--docs Storybook Reference}
 */

@Component({
  selector: 'fin-stepper-field',
  standalone: true,
  imports: [
    CommonModule,
    FinAngularMaterialModule,
    ReactiveFormsModule,
    FinIconModule,
  ],
  templateUrl: './stepper-field.component.html',
  styleUrl: './stepper-field.component.scss',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => FinStepperFieldComponent),
      multi: true,
    },
    FinFieldService,
    FinFieldMessageService,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinStepperFieldComponent
  extends FinControlValueAccessor
  implements AfterViewInit
{
  /** Label for the input field */
  @Input() label = '';

  /** Defines the size of the input field. */
  @Input() size: FinSize.M | FinSize.L = FinSize.M;

  /** Specifies the lowest value the user can set using the stepper controls. */
  @Input() min!: number | undefined;

  /** Specifies the highest value the user can set using the stepper controls. */
  @Input() max!: number | undefined;

  /** To be used only with external messages */
  @Input() externalFieldMessages: FinFieldMessageBodyDirective | null = null;

  /** Switch readonly mode. */
  @Input({ transform: booleanAttribute }) readonly = false;

  /** Event emitted when the input value has been changed. */
  @Output() inputChange = new EventEmitter<string>();

  protected sizes = FinSize;

  /** Returns the active validation message. */
  protected getMessage$ = this.finFieldMessageService?.getMessage$.pipe(
    shareReplay({ refCount: true, bufferSize: 1 }),
  );

  constructor(
    injector: Injector,
    @Optional() private finFieldMessageService: FinFieldMessageService,
    private finInputService: FinFieldService,
    private destroyRef: DestroyRef,
  ) {
    super(injector);
  }

  ngAfterViewInit(): void {
    this.control.statusChanges
      .pipe(startWith(this.control.errors), takeUntilDestroyed(this.destroyRef))
      .subscribe(() => {
        this.finInputService.controlErrors$.next(this.control.errors);
      });
  }

  protected decrease() {
    this.control.setValue(+this.control.value - 1);
    this.onInputChange();
  }

  protected increase() {
    this.control.setValue(+this.control.value + 1);
    this.onInputChange();
  }

  protected onInputChange() {
    this.inputChange.emit(this.control.value);
  }
}
