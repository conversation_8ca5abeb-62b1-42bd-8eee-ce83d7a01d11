import { CommonModule } from '@angular/common';
import { FormControl, Validators } from '@angular/forms';
import { FinFieldMessageModule } from '@fincloud/ui/field-message';
import { FinSize } from '@fincloud/ui/types';
import { moduleMetadata, type Meta, type StoryObj } from '@storybook/angular';
import { FinStepperFieldModule } from '../../stepper-field.module';
import { FinStepperFieldComponent } from './stepper-field.component';

const meta: Meta = {
  component: FinStepperFieldComponent,
  title: 'Fields/Stepper Field',
  parameters: {
    docs: {
      description: {
        component:
          '`import { FinStepperFieldModule } from "@fincloud/ui/stepper-field"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=26051-100873',
    },
  },
  decorators: [
    moduleMetadata({
      imports: [CommonModule, FinStepperFieldModule, FinFieldMessageModule],
    }),
  ],
};
export default meta;
type Story = StoryObj<FinStepperFieldComponent>;

const disabledField = new FormControl({ value: '5', disabled: true });

const invalidField = new FormControl('', Validators.required);
invalidField.markAsTouched();

const validField = new FormControl('5', Validators.required);
validField.markAsTouched();

export const Primary: Story = {
  args: {
    label: 'Stepper label',
    size: FinSize.M,
    min: undefined,
    max: undefined,
  },
  argTypes: {
    size: {
      options: [FinSize.M, FinSize.L],
      control: { type: 'select' },
    },
    inputChange: {
      control: false,
    },
  },
  render: (args) => ({
    props: { ...args, formControl: new FormControl('') },
  }),
};

export const WithMinAndMax: Story = {
  args: {
    ...Primary.args,
    min: 3,
    max: 8,
  },
  argTypes: {
    ...Primary.argTypes,
  },
  render: (args) => ({
    props: { ...args, formControl: new FormControl(3) },
  }),
};

export const ValidationSuccess: Story = {
  args: {
    ...Primary.args,
  },
  argTypes: {
    ...Primary.argTypes,
  },
  render: (args) => {
    return {
      props: { ...args, formControl: validField },
      template: `
      <fin-stepper-field
        [formControl]="formControl"
        [label]="'Stepper label'"
        [size]="'m'"
        [min]="3"
        [max]="8"
      >
        <fin-field-messages>
          <ng-template finFieldMessage type="success">
            Some success message.
          </ng-template>
        </fin-field-messages>   
      </fin-stepper-field>`,
    };
  },
};

export const ValidationError: Story = {
  args: {
    ...Primary.args,
  },
  argTypes: {
    ...Primary.argTypes,
  },
  render: (args) => {
    return {
      props: { ...args, formControl: invalidField },
      template: `
      <fin-stepper-field
        [formControl]="formControl"
        [label]="'Stepper label'"
        [size]="'m'"
        [min]="3"
        [max]="8"
      >
        <fin-field-messages>
          <ng-template finFieldMessage type="error" errorKey="required">
            Some error message.
          </ng-template>
        </fin-field-messages>   
      </fin-stepper-field>`,
    };
  },
};

export const ValidationWarning: Story = {
  args: {
    ...Primary.args,
  },
  argTypes: {
    ...Primary.argTypes,
  },
  render: (args) => ({
    props: { ...args, formControl: invalidField },
    template: `
      <fin-stepper-field
        [formControl]="formControl"
        label="Stepper label"
      >
        <fin-field-messages>
          <ng-template finFieldMessage type="warning" errorKey="required">
            This field is required.
          </ng-template>
        </fin-field-messages>          
      </fin-stepper-field>
    `,
  }),
};

export const Disabled: Story = {
  args: {
    ...Primary.args,
  },
  argTypes: {
    ...Primary.argTypes,
  },
  render: (args) => ({
    props: { ...args, formControl: disabledField },
  }),
};

export const Readonly: Story = {
  args: {
    ...Primary.args,
    readonly: true,
  },
  argTypes: {
    ...Primary.argTypes,
  },
  render: (args) => ({
    props: { ...args, formControl: disabledField },
  }),
};

export const Hint: Story = {
  args: {
    ...Primary.args,
  },
  argTypes: {
    ...Primary.argTypes,
  },
  render: (args) => ({
    props: { ...args, formControl: new FormControl() },
    template: `
      <fin-stepper-field
        [formControl]="formControl"
        label="Stepper label"
      >
        <ng-container finInputHint>
          Some hint text
        </ng-container>
      </fin-stepper-field>
    `,
  }),
};
