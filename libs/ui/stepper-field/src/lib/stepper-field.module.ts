import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FinStepperFieldComponent } from './components/stepper-field/stepper-field.component';
import { FinMarkAllAsTouchedDirective } from '@fincloud/utils/directives';
import { FinFormService } from '@fincloud/utils/services';

@NgModule({
  imports: [
    CommonModule,
    FinStepperFieldComponent,
    FinMarkAllAsTouchedDirective,
  ],
  exports: [FinStepperFieldComponent, FinMarkAllAsTouchedDirective],
  providers: [FinFormService],
})
export class FinStepperFieldModule {}
