import { TemplateRef } from '@angular/core';
import { FinTreeNodeContext } from '../models/fin-tree-node-context';
import { FinTreeNodeDirective } from './tree-node.directive';

describe('FinTreeNodeDirective', () => {
  it('should create an instance', () => {
    const mockTemplateRef = {} as TemplateRef<FinTreeNodeContext<unknown>>;
    const directive = new FinTreeNodeDirective(mockTemplateRef);
    expect(directive).toBeTruthy();
  });
});
