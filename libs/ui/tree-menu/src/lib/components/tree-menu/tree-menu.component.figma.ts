import figma, { html } from '@figma/code-connect/html';

figma.connect(
  'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=28467-12861',
  {
    props: {},
    example: () =>
      html`<fin-tree-menu [data]="data">
        <ng-template finTreeNode let-node> {{ node.label }} </ng-template>
        <ng-template
          finTreeNode
          let-node
          templateName="field"
          appearance="secondary"
        >
          {{ node.label }}
        </ng-template>
        <ng-template
          finTreeNode
          let-node
          templateName="folderRoot"
          appearance="secondary"
        >
          <span class="fin-font-semibold">{{ node.label }}</span>
        </ng-template>
        <ng-template
          finTreeNode
          let-node
          templateName="folder"
          appearance="secondary"
        >
          <fin-icon name="folder" size="S" />
          {{ node.label }}
        </ng-template>
        <ng-template
          finTreeNode
          let-node
          templateName="document"
          appearance="secondary"
        >
          <fin-icon name="article" size="S" />
          {{ node.label }}
        </ng-template>
      </fin-tree-menu>`,
    imports: [
      "import { FinTreeMenuModule, MatTreeModule, FinTreeNodeDirective } from '@fincloud/ui/tree-menu'",
    ],
  },
);
