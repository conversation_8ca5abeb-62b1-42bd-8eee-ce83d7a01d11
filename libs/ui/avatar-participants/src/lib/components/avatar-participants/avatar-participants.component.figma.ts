import figma, { html } from '@figma/code-connect/html';
import { FinSize } from '@fincloud/ui/types';
import { FinParticipantType } from '../../enums/fin-participant-type';

figma.connect(
  'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=41568-424',
  {
    props: {
      participantType: figma.enum('Type', {
        Participant: FinParticipantType.PARTICIPANT,
        Leader: FinParticipantType.LEADER,
      }),
      size: figma.enum('Size', {
        'XS-16': FinSize.XS,
        'S-24': FinSize.S,
        'M-32': FinSize.M,
        'L-40': FinSize.L,
        'XL-56': FinSize.XL,
        'XXL-96': FinSize.XXL,
      }),
    },
    example: (props) =>
      html`<fin-avatar-participants
        participantType="${props.participantType}"
        size="${props.size}"
      ></fin-avatar-participants>`,
    imports: [
      "import { FinAvatarParticipantsModule } from '@fincloud/ui/avatar-participants'",
    ],
  },
);
