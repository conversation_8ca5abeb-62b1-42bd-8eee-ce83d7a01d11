import { FinSize } from '@fincloud/ui/types';
import type { Meta, StoryObj } from '@storybook/angular';
import { FinParticipantType } from '../../enums/fin-participant-type';
import { FinAvatarParticipantsComponent } from './avatar-participants.component';

const meta: Meta<FinAvatarParticipantsComponent> = {
  component: FinAvatarParticipantsComponent,
  title: 'Components/Avatars/Avatar Participants',
  parameters: {
    docs: {
      description: {
        component:
          '`import { FinAvatarParticipantsModule } from "@fincloud/ui/avatar-participants"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-WIP?type=design&node-id=6528-194&mode=dev',
    },
  },
};
export default meta;
type Story = StoryObj<FinAvatarParticipantsComponent>;

export const Participant: Story = {
  argTypes: {
    participantType: {
      options: Object.values(FinParticipantType),
      control: { type: 'select' },
    },
    size: {
      options: Object.values(FinSize),
      control: { type: 'select' },
    },
  },
  args: {
    participantType: FinParticipantType.PARTICIPANT,
    size: FinSize.M,
  },
};

export const Leader: Story = {
  argTypes: {
    ...Participant.argTypes,
  },
  args: {
    ...Participant.args,
    participantType: FinParticipantType.LEADER,
  },
  parameters: {
    ...Participant.parameters,
  },
};
