import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { FinSize } from '@fincloud/ui/types';
import { FinParticipantType } from '../../enums/fin-participant-type';

/**
 * Shows a participant avatars.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-avatar-participants--docs Storybook Reference}
 */
@Component({
  selector: 'fin-avatar-participants',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './avatar-participants.component.html',
  styleUrl: './avatar-participants.component.scss',
  host: {
    '[class]': 'finAvatarParticipantsCssClasses',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinAvatarParticipantsComponent {
  /** Specifies the avatar type.*/
  @Input() participantType: FinParticipantType = FinParticipantType.PARTICIPANT;

  /** Size of the avatar. */
  @Input() size: FinSize = FinSize.M;

  /** <span class="fin-text-red-500 fin-font-bold">Deprecated</span> */
  @Input() showType = false;

  protected get finAvatarParticipantsCssClasses(): string {
    return `fin-avatar-participants fin-avatar-participants-${this.size}`;
  }
}
