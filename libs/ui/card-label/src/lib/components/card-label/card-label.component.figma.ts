import figma, { html } from '@figma/code-connect/html';
import { FinSize } from '@fincloud/ui/types';

figma.connect(
  'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=24125-52174',
  {
    props: {
      size: figma.enum('Size', {
        S: FinSize.XS,
        M: FinSize.XS,
      }),
    },
    example: (props) =>
      html`<fin-card-label
        size="${props.size}"
        label=""
        iconName=""
        iconSrc=""
      ></fin-card-label>`,
    imports: ["import { FinCardLabelModule } from '@fincloud/ui/card-label'"],
  },
);
