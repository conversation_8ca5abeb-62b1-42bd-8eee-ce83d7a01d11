{
  // Place your lib-ui workspace snippets here. Each snippet is defined under a snippet name and has a scope, prefix, body and
  // description.

  "Action Menu": {
    "scope": "html",
    "prefix": "fin-action-menu",
    "body": [
      "<button type=\"button\" [finActionMenuTrigger]=\"finMenu.panel\">",
      "  <fin-icon name=\"more_vert\" [size]=\"'l'\"></fin-icon>",
      "</button>",
      "<fin-actions-menu #finMenu=\"finActionMenu\">",
      "  @for (item of ${1:menuItems}; track item.id) {",
      "    <button fin-menu-item (click)=\"item.action()\">",
      "      <fin-icon class=\"fin-icon\" matIconOutlined name=\"item.icon\"></fin-icon>",
      "      {{ item.label }}",
      "    </button>",
      "  }",
      "</fin-actions-menu>",
    ],
    "description": "Snippet for using the action menu component",
  },
  "Fin Asset Card": {
    "scope": "html",
    "prefix": "fin-asset-card",
    "body": [
      "<fin-asset-card",
      "  [label]=\"${1:label}\"",
      "  [description]=\"${2:description}\"",
      "  [backgroundImage]=\"${3:backgroundImage}\"",
      "  [opacityBackground]=\"${4:opacityBackground}\"",
      ">",
      "  <ng-template #finAssetCardHeader></ng-template>",
      "",
      "  <ng-template #finAssetCardItemContainer>",
      "    <fin-asset-card-item",
      "      [icon]=\"${5:cardIcon}\"",
      "      [description]=\"${6:cardDescription}\"",
      "    ></fin-asset-card-item>",
      "  </ng-template>",
      "</fin-asset-card>",
    ],
    "description": "Snippet for using the fin-asset-card component with header and item container templates",
  },
  "Badge Icon": {
    "scope": "html",
    "prefix": "fin-badge-icon",
    "body": [
      "<fin-badge-icon",
      "  [type]=\"${1:FinBadgeType.DEFAULT}\"",
      "  [size]=\"${2:FinSize.M}\"",
      "  name=\"${3:iconName}\"",
      "  src=\"${4:iconSource}\">",
      "</fin-badge-icon>",
    ],
    "description": "Snippet for using the badge icon component",
  },
  "Badge Indicator": {
    "scope": "html",
    "prefix": "fin-badge-indicator",
    "body": [
      "<fin-badge-indicator",
      "  [count]=\"${1:0}\"",
      "  [type]=\"${2:FinBadgeType.DEFAULT}\"",
      "  [size]=\"${3:FinSize.M}\"",
      "  [maxCount]=\"${4:null}\">",
      "</fin-badge-indicator>",
    ],
    "description": "Snippet for using the badge indicator component",
  },
  "Badge Status": {
    "scope": "html",
    "prefix": "fin-badge-status",
    "body": [
      "<fin-badge-status",
      "  [type]=\"${1:FinBadgeStatus.SIGNED}\"",
      "  text=\"${2:Status Text}\">",
      "</fin-badge-status>",
    ],
    "description": "Snippet for using the badge status component",
  },
  "Breadcrumb": {
    "scope": "html",
    "prefix": "fin-breadcrumb",
    "body": [
      "<fin-breadcrumb",
      "  [menuItems]=\"${1:menuItems}\"",
      "  (backButtonClicked)=\"${2:onBackButtonClicked()}\">",
      "</fin-breadcrumb>",
    ],
    "description": "Snippet for using the breadcrumb component",
  },
  "Button": {
    "scope": "html",
    "prefix": "fin-button",
    "body": ["<button fin-button>", "  ${1:Button Label}", "</button>"],
    "description": "Snippet for using the button component without options",
  },
  "Button Action": {
    "scope": "html",
    "prefix": "fin-button-action",
    "body": [
      "<button fin-button-action>",
      "  [type]=\"${1:FinButtonActionType.PRIMARY}\"",
      "  [size]=\"${2:FinSize.XL}\"",
      "</button>",
    ],
    "description": "Snippet for using the button action component",
  },
  "Button - Full": {
    "scope": "html",
    "prefix": "fin-button-full",
    "body": [
      "<button fin-button",
      "  [size]=\"${1:FinSize.M}\"",
      "  [shape]=\"${2:FinButtonShape.ROUND}\"",
      "  [appearance]=\"${3:FinButtonAppearance.PRIMARY}\"",
      "  [isActive]=\"${4:false}\"",
      "  [attention]=\"${5:false}\"",
      "  [showLoader]=\"${6:false}\">",
      "  ${7:Button Label}",
      "</button>",
    ],
    "description": "Snippet for using the button component with all options",
  },
  "Button with Loader": {
    "scope": "html",
    "prefix": "fin-button-loader",
    "body": [
      "<button fin-button",
      "  [showLoader]=\"${1:true}\">",
      "  ${2:Button Label}",
      "</button>",
    ],
    "description": "Snippet for using the button component with loader",
  },
  "Button Icon": {
    "scope": "html",
    "prefix": "fin-button-icon",
    "body": [
      "<button fin-button-icon>",
      "  <fin-icon name=\"${1:iconName}\"></fin-icon>",
      "</button>",
    ],
    "description": "Snippet for using the button icon component",
  },
  "Button Link": {
    "scope": "html",
    "prefix": "fin-button-link",
    "body": ["<a fin-button-link>", "  ${1:Link Text}", "</a>"],
    "description": "Snippet for using the button link component",
  },
  "Button Fab": {
    "scope": "html",
    "prefix": "fin-button-fab",
    "body": [
      "<button fin-button-fab",
      "  [size]=\"${1:FinSize.S}\">",
      "  <fin-icon src=\"${2:iconSource}\"></fin-icon>",
      "</button>",
    ],
    "description": "Snippet for using the button fab component",
  },
  "Card Label": {
    "scope": "html",
    "prefix": "fin-card-label",
    "body": [
      "<fin-card-label",
      "  [label]=\"${1:Card Label}\">",
      "</fin-card-label>",
    ],
    "description": "Snippet for using the card label component",
  },
  "Area Chart": {
    "scope": "html",
    "prefix": "fin-area-chart",
    "body": [
      "<fin-area-chart",
      "  [labels]=\"${1:['January', 'February', 'March', 'April']}\"",
      "  [values]=\"${2:[10, 20, 15, 30]}\">",
      "</fin-area-chart>",
    ],
    "description": "Snippet for using the area chart component",
  },
  "Bar Chart": {
    "scope": "html",
    "prefix": "fin-bar-chart",
    "body": [
      "<fin-bar-chart",
      "  [labels]=\"${1:['January', 'February', 'March', 'April', 'May']}\"",
      "  [bars]=\"${2:[{ label: 'Dataset 1', data: [12, 19, 3, 5, 2], backgroundColor: '#CDCFF6' }]}\"",
      "</fin-bar-chart>",
    ],
    "description": "Snippet for using the bar chart component",
  },
  "Document Classification": {
    "scope": "html",
    "prefix": "fin-document-classification",
    "body": [
      "<fin-document-classification",
      "  [formControl]=\"${1:formControl}\"",
      "  [placeholder]=\"${2:placeholder}\"",
      "  [readonly]=\"${3:readonly}\"",
      "  [expanded]=\"${4:expanded}\"",
      "  [loading]=\"${5:loading}\"",
      ">",
      "  <ng-template #finPrefix>",
      "    ${6:Prefix}",
      "  </ng-template>",
      "",
      "  <ng-template #finInputPrefix>",
      "    ${7:Input Prefix}",
      "  </ng-template>",
      "",
      "  <ng-template #finContent>",
      "    ${8:Content}",
      "  </ng-template>",
      "</fin-document-classification>",
    ],
    "description": "Snippet for using the document classification component with templates",
  },
  "Doughnut Chart": {
    "scope": "html",
    "prefix": "fin-doughnut-chart",
    "body": [
      "<fin-doughnut-chart",
      "  [labels]=\"${1:['Label 1', 'Label 2']}\"",
      "  [values]=\"${2:[35, 65]}\">",
      "</fin-doughnut-chart>",
    ],
    "description": "Snippet for using the doughnut chart component",
  },
  "Checkbox": {
    "scope": "html",
    "prefix": "fin-checkbox",
    "body": [
      "<fin-checkbox",
      "  [formControl]=\"${1:formControl}\"",
      "  [label]=\"'${2:Option label}'\"",
      "  [size]=\"${3:FinSize.M}\">",
      "</fin-checkbox>",
    ],
    "description": "Snippet for using the checkbox component with FormControl",
  },
  "Container": {
    "scope": "html",
    "prefix": "fin-container",
    "body": [
      "<div finContainer",
      "  [boxShadow]=\"${1:true}\"",
      "  [borderRadius]=\"${2:true}\">",
      "  ${3:Custom content}",
      "</div>",
    ],
    "description": "Snippet for using the container directive",
  },
  "Date Picker": {
    "scope": "html",
    "prefix": "fin-date-picker",
    "body": [
      "<fin-date-picker",
      "  [formControl]=\"${1:formControl}\"",
      "  [label]=\"'${2:label}'\"",
      "  [placeholder]=\"'${3:placeholder}'\"",
      "  [showIcon]=\"${4:showIcon}\"",
      "  [showButtonBar]=\"${5:showButtonBar}\"",
      ">",
      "</fin-date-picker>",
    ],
    "description": "Snippet for using the date picker component",
  },
  "Dropdown": {
    "scope": "html",
    "prefix": "fin-dropdown",
    "body": [
      "<fin-dropdown",
      "  [formControl]=\"${1:formControl}\"",
      "  [label]=\"'${2:Dropdown label}'\"",
      "  [placeholder]=\"'${3:Placeholder}'\"",
      "  [options]=\"${4:options}\">",
      "</fin-dropdown>",
    ],
    "description": "Snippet for using the dropdown component",
  },
  "Dropdown with Field Error": {
    "scope": "html",
    "prefix": "fin-dropdown-error",
    "body": [
      "<fin-dropdown",
      "  [formControl]=\"${1:formControl}\"",
      "  [label]=\"'${2:Dropdown label}'\"",
      "  [placeholder]=\"'${3:Placeholder}'\"",
      "  [options]=\"${4:options}\">",
      "  <ng-container finFieldError>",
      "    <fin-field-error errorType=\"required\">${5:This field is required}</fin-field-error>",
      "    <fin-field-error errorType=\"minlength\">${6:The field should be longer than 10}</fin-field-error>",
      "  </ng-container>",
      "</fin-dropdown>",
    ],
    "description": "Snippet for using the dropdown component with field error",
  },

  "Empty State": {
    "scope": "html",
    "prefix": "fin-empty-state",
    "body": [
      "<fin-empty-state",
      "  class=\"fin-m-12\"",
      "  [type]=\"${1:emptyStateType.COMPLEX}\"",
      "  title=\"${2:No content}\"",
      ">",
      "  <ng-template #finIcon>",
      "    <img",
      "      [src]=\"'assets/storybook/icons/document.svg'\"",
      "      class=\"fin-w-[8.8rem]\"",
      "    />",
      "  </ng-template>",
      "  <ng-template #finTextContent>",
      "    ${3:<!-- finTextContent template content -->}",
      "  </ng-template>",
      "  <ng-template #finDescription>",
      "    ${4:<!-- finDescription template content -->}",
      "    <a fin-button-link class=\"fin-text-[1.4rem]\">${5:ac venenatis.}</a>",
      "  </ng-template>",
      "  <ng-template #finActions>",
      "    <button",
      "      fin-button",
      "      [size]=\"size.M\"",
      "      [shape]=\"shape.ROUND\"",
      "      [appearance]=\"appearance.SECONDARY\"",
      "    >",
      "      ${6:Action}",
      "    </button>",
      "    <button",
      "      fin-button",
      "      [size]=\"size.M\"",
      "      [shape]=\"shape.ROUND\"",
      "      [appearance]=\"appearance.PRIMARY\"",
      "    >",
      "      ${7:Action}",
      "    </button>",
      "  </ng-template>",
      "</fin-empty-state>",
    ],
    "description": "Snippet for using the empty state component with all of its options",
  },

  "Accordion": {
    "scope": "html",
    "prefix": "fin-accordion",
    "body": [
      "<fin-accordion",
      "  [multi]=\"${1:false}\"",
      "  [hideToggle]=\"${2:false}\"",
      "  [togglePosition]=\"${3:FinAccordionTogglePosition.AFTER}\"",
      "  [toggleDirection]=\"${4:FinAccordionToggleDirection.AUTO}\"",
      "  [size]=\"${5:FinSize.S}\"",
      "  [borderless]=\"${6:false}\"",
      "  [type]=\"${7:FinAccordionType.DEFAULT}\">",
      "  @for (panel of ${8:panels}; track panel.id) {",
      "    <fin-expansion-panel",
      "      [disabled]=\"panel.disabled\"",
      "      [expanded]=\"panel.expanded\"",
      "      [isSummaryAlwaysVisible]=\"panel.isSummaryAlwaysVisible\">",
      "      <ng-template #finTitle>",
      "        {{ panel.title }}",
      "      </ng-template>",
      "      <ng-template #finDescription>",
      "        {{ panel.description }}",
      "      </ng-template>",
      "      <ng-template #finSummary>",
      "        {{ panel.summary }}",
      "      </ng-template>",
      "      <ng-template #finContent>",
      "        {{ panel.content }}",
      "      </ng-template>",
      "    </fin-expansion-panel>",
      "  }",
      "</fin-accordion>",
    ],
    "description": "Snippet for using the accordion component",
  },
  "Filter Tabs": {
    "scope": "html",
    "prefix": "fin-filter-tabs",
    "body": [
      "<fin-filter-tabs",
      "  [formControl]=\"${1:formControl}\"",
      "  [label]=\"'${2:Tab Label}'\"",
      "  [count]=\"${3:0}\"",
      "  [value]=\"'${4:Tab Value}'\"",
      "  [isSelected]=\"${5:false}\"",
      "  (selectedTabChange)=\"${6:onSelectedTabChange($event)}\">",
      "</fin-filter-tabs>",
    ],
    "description": "Snippet for using the filter tabs component",
  },

  "Header Component": {
    "scope": "html",
    "prefix": "fin-header",
    "body": ["<fin-header [size]=\"${1:size}\"></fin-header>"],
    "description": "Snippet for using the fin-header component",
  },

  "Footer Component": {
    "scope": "html",
    "prefix": "fin-footer",
    "body": ["<fin-footer [size]=\"${1:size}\"></fin-footer>"],
    "description": "Snippet for using the fin-footer component",
  },

  "Icon": {
    "scope": "html",
    "prefix": "fin-icon",
    "body": [
      "<fin-icon",
      "  name=\"${1:home}\"",
      "  [size]=\"${2:FinSize.M}\">",
      "</fin-icon>",
    ],
    "description": "Snippet for using the icon component",
  },
  "Input": {
    "scope": "html",
    "prefix": "fin-input",
    "body": [
      "<fin-input",
      "  [formControl]=\"${1:formControl}\"",
      "  [label]=\"'${2:Input label}'\"",
      "  [placeholder]=\"'${3:Placeholder}'\">",
      "</fin-input>",
    ],
    "description": "Snippet for using the input component",
  },
  "Input with Field Error": {
    "scope": "html",
    "prefix": "fin-input-error",
    "body": [
      "<fin-input",
      "  [formControl]=\"${1:formControl}\"",
      "  [label]=\"'${2:Input label}'\"",
      "  [placeholder]=\"'${3:Enter text}'\">",
      "  <fin-field-messages>",
      "    <ng-template finFieldMessage type=\"${4:error}\" errorKey=\"${5:required}\">",
      "${6:This field is required}",
      "    </ng-template>",
      "</fin-field-messages>",
      "</fin-input>",
    ],
    "description": "Snippet for using the input component with field error",
  },
  "Field Error": {
    "scope": "html",
    "prefix": "fin-field-error",
    "body": [
      "<fin-field-error",
      "  errorType=\"${1:required}\">",
      "  ${2:This field is required}",
      "</fin-field-error>",
    ],
    "description": "Snippet for using the field error component",
  },
  "Loader": {
    "scope": "html",
    "prefix": "fin-loader",
    "body": ["<fin-loader", "  [hide]=\"${1:false}\">", "</fin-loader>"],
    "description": "Snippet for using the loader component",
  },

  "Modal": {
    "scope": "html",
    "prefix": "fin-modal",
    "body": [
      "<fin-modal-header>",
      "  ${1:Header}",
      "  <button fin-button-action fin-modal-close>",
      "    <fin-icon name=\"close\"></fin-icon>",
      "  </button>",
      "</fin-modal-header>",
      "",
      "<fin-modal-content></fin-modal-content>",
      "",
      "<fin-modal-footer>",
      "  <button fin-button fin-modal-close [appearance]=\"appearance.SECONDARY\">",
      "    ${6:Close}",
      "  </button>",
      "  <button fin-button [fin-modal-close]=\"true\" [appearance]=\"appearance.PRIMARY\">",
      "    ${7:Action}",
      "  </button>",
      "</fin-modal-footer>",
    ],
    "description": "Snippet for using the fin-modal component",
  },

  "Paginator": {
    "scope": "html",
    "prefix": "fin-paginator",
    "body": [
      "<fin-paginator",
      "  [totalItems]=\"${1:500}\"",
      "  [pageSize]=\"${2:20}\"",
      "  [pageNumber]=\"${3:5}\"",
      "  [pageSizeOptions]=\"${4:[10, 20, 50, 100]}\"",
      "  (pageChange)=\"${5:onPageChange($event)}\">",
      "</fin-paginator>",
    ],
    "description": "Snippet for using the paginator component",
  },
  "PdfViewer": {
    "scope": "html",
    "prefix": "fin-pdf-viewer",
    "body": [
      "<fin-modal-content>",
      "  <fin-pdf-viewer",
      "    [headerFilename]=\"${1:headerFilename}\"",
      "    [src]=\"${2:src}\"",
      "    [uploadedBy]=\"${3:uploadedBy}\"",
      "    [organisation]=\"${4:organisation}\"",
      "    [source]=\"${5:source}\">",
      "  </fin-pdf-viewer>",
      "</fin-modal-content>",
    ],
    "description": "Snippet for using the PDF viewer component inside a modal",
  },
  "Percentage Loader": {
    "scope": "html",
    "prefix": "fin-percentage-loader",
    "body": [
      "<fin-percentage-loader",
      "  [percentage]=\"${1:25}\">",
      "</fin-percentage-loader>",
    ],
    "description": "Snippet for using the percentage loader component",
  },
  "Progress Bar": {
    "scope": "html",
    "prefix": "fin-progress-bar",
    "body": [
      "<fin-progress-bar",
      "  [segments]=\"${1:segments}\">",
      "</fin-progress-bar>",
    ],
    "description": "Snippet for using the progress bar component",
  },
  "Range Slider": {
    "scope": "html",
    "prefix": "fin-range-slider",
    "body": [
      "<fin-range-slider",
      "  [min]=\"${1:min}\"",
      "  [max]=\"${2:max}\"",
      "  [step]=\"${3:step}\"",
      "  [startThumbControl]=\"${4:startControl}\"",
      "  [endThumbControl]=\"${5:endControl}\"",
      "></fin-range-slider>",
    ],
    "description": "Snippet for using the range slider component",
  },
  "Radio Button": {
    "scope": "html",
    "prefix": "fin-radio-button",
    "body": [
      "<fin-radio-button",
      "  [formControl]=\"${1:formControl}\"",
      "  [options]=\"${2:options}\">",
      "</fin-radio-button>",
    ],
    "description": "Snippet for using the radio button component",
  },
  "Scrollbar": {
    "scope": "html",
    "prefix": "fin-scrollbar",
    "body": [
      "<fin-scrollbar",
      "  class=\"${1:fin-h-[30rem]}\">",
      "</fin-scrollbar>",
    ],
    "description": "Snippet for using the scrollbar component",
  },
  "Search": {
    "scope": "html",
    "prefix": "fin-search",
    "body": [
      "<fin-search",
      "  [formControl]=\"${1:formControl}\"",
      "  (inputChange)=\"${2:inputChange($event)}\"",
      "  placeholder=\"${3:placeholder}\"",
      "></fin-search>",
    ],
    "description": "Snippet for using the fin-search component",
  },
  "Search with autocomplete": {
    "scope": "html",
    "prefix": "fin-search-autocomplete",
    "body": [
      "<fin-search",
      "  [formControl]=\"${1:formControl}\"",
      "  ${2|autocomplete,|}",
      "  [options]=\"${3:options}\"",
      "  (inputChange)=\"${4:inputChange($event)}\"",
      "  (autoCompleteOptionChange)=\"${5:autocompleteOptionChange($event)}\"",
      "  (inputFocus)=\"${6:inputFocus()}\"",
      "  placeholder=\"${7:placeholder}\"",
      "></fin-search>",
    ],
    "description": "Snippet for using the fin-search component",
  },
  "Horizontal Separator": {
    "scope": "html",
    "prefix": "fin-horizontal-separator",
    "body": [
      "<hr finHorizontalSeparator",
      "  [type]=\"${1:FinSeparator.MINIMAL}\">",
    ],
    "description": "Snippet for using the horizontal separator directive",
  },
  "Vertical Separator": {
    "scope": "html",
    "prefix": "fin-vertical-separator",
    "body": [
      "<div finVerticalSeparator",
      "  [type]=\"${1:FinSeparator.MINIMAL}\">",
      "</div>",
    ],
    "description": "Snippet for using the vertical separator directive",
  },
  "Side Panel": {
    "scope": "html",
    "prefix": "fin-side-panel",
    "body": [
      "<fin-side-panel",
      "  #sidePanel",
      "  [open]=\"${1:true}\">",
      "  <ng-template finSidePanelSlide>",
      "    <div class=\"fin-h-full fin-p-[1rem]\">",
      "      ${2:Slide details}",
      "    </div>",
      "  </ng-template>",
      "  <ng-template finSidePanelContent>",
      "    <div class=\"fin-h-full fin-p-[1rem] fin-bg-color-surface-secondary\">",
      "      ${3:Content details.}",
      "      <button fin-button (click)=\"sidePanel.toggleSidePanel()\" size=\"s\">",
      "        Toggle side panel",
      "      </button>",
      "    </div>",
      "  </ng-template>",
      "</fin-side-panel>",
    ],
    "description": "Snippet for using the side panel container component",
  },

  "Menu Item - Default Button": {
    "scope": "html",
    "prefix": "fin-menu-item-default-button",
    "body": [
      "<button",
      "  fin-menu-item",
      "  [type]=\"${1:type}\"",
      "  [active]=\"${2:false}\"",
      "  [iconName]=\"'${3:}'\"",
      "  <ng-container finMenuItemTitle>",
      "    ${4:Title}",
      "  </ng-container>",
      "  <ng-container finMenuItemDescription>",
      "    ${5:Description}",
      "  </ng-container>",
      "  <ng-container finMenuItemSuffix>",
      "    ${6:Suffix}",
      "  </ng-container>",
      "</button>",
    ],
    "description": "Snippet for using the fin-menu-item, default type, button component",
  },

  "Menu Item - Default Anchor": {
    "scope": "html",
    "prefix": "fin-menu-item-default-anchor",
    "body": [
      "<a",
      "  fin-menu-item",
      "  [type]=\"${1:type}\"",
      "  [active]=\"${2:false}\"",
      "  [iconName]=\"'${3:}'\"",
      "  <ng-container finMenuItemTitle>",
      "    ${4:Title}",
      "  </ng-container>",
      "  <ng-container finMenuItemDescription>",
      "    ${5:Description}",
      "  </ng-container>",
      "  <ng-container finMenuItemSuffix>",
      "    ${6:Suffix}",
      "  </ng-container>",
      "</a>",
    ],
    "description": "Snippet for using the fin-menu-item, default type, anchor component",
  },

  "Menu Item - Platform Button": {
    "scope": "html",
    "prefix": "fin-menu-item-platform-button",
    "body": [
      "<button",
      "  fin-menu-item",
      "  [type]=\"${1:type}\"",
      "  [compact]=\"${2:false}\"",
      "  [active]=\"${3:false}\"",
      "  [iconName]=\"'${4:}'\"",
      "  <ng-container finMenuItemTitle>",
      "    ${5:Title}",
      "  </ng-container>",
      "</button>",
    ],
    "description": "Snippet for using the fin-menu-item, platform type, button component",
  },

  "Menu Item - Platform Anchor": {
    "scope": "html",
    "prefix": "fin-menu-item-anchor-button",
    "body": [
      "<a",
      "  fin-menu-item",
      "  [type]=\"${1:type}\"",
      "  [compact]=\"${2:false}\"",
      "  [active]=\"${3:false}\"",
      "  [iconName]=\"'${4:}'\"",
      "  <ng-container finMenuItemTitle>",
      "    ${5:Title}",
      "  </ng-container>",
      "</a>",
    ],
    "description": "Snippet for using the fin-menu-item, platform type, anchor component",
  },

  "Slide Toggle": {
    "scope": "html",
    "prefix": "fin-slide-toggle",
    "body": [
      "<fin-slide-toggle",
      "  [formControl]=\"${1:formControl}\"",
      "  [label]=\"'${2:label}'\">",
      "</fin-slide-toggle>",
    ],
    "description": "Snippet for using the slide toggle component",
  },
  "Split Button": {
    "scope": "html",
    "prefix": "fin-split-button",
    "body": [
      "<fin-split-button",
      "  [label]=\"'${1:Button}'\"",
      "  [options]=\"${2:options}\">",
      "</fin-split-button>",
    ],
    "description": "Snippet for using the split button component",
  },
  "Stepper Field": {
    "scope": "html",
    "prefix": "fin-stepper-field",
    "body": [
      "<fin-stepper-field",
      "  label=\"'${1:Stepper label}'\"",
      "  [formControl]=\"${2:formControl}\"",
      "  [min]=\"${3:min}\"",
      "  [max]=\"${4:max}\"",
      "  (inputChange)=\"${5:inputChange}($event)\"\">",
      "</fin-stepper-field>",
    ],
    "description": "Snippet for using the stepper field component",
  },
  "Switch Toggle": {
    "scope": "html",
    "prefix": "fin-switch-toggle",
    "body": [
      "<fin-switch-toggle",
      "  [formControl]=\"${1:formControl}\"",
      "  [options]=\"${2:options}\">",
      "  [stretched]=\"${3:boolean}\">",
      "</fin-switch-toggle>",
    ],
    "description": "Snippet for using the switch toggle component",
  },
  "Table": {
    "scope": "html",
    "prefix": "fin-table",
    "body": [
      "<fin-table",
      "  [rows]=\"${1:rows}\"",
      "  [columns]=\"${2:columns}\"",
      "  [emptyMessage]=\"'${3:No data available}'\">",
      "  <ng-container rowTemplates>",
      "    <ng-template name=\"${4:name}\" [finRowTemplate]=\"${5:rows}\" let-row>",
      "      ${6:<!-- Row template content -->}",
      "    </ng-template>",
      "    <!-- Additional templates -->",
      "  </ng-container>",
      "</fin-table>",
    ],
    "description": "Snippet for using the table component",
  },
  "Table - Full": {
    "scope": "html",
    "prefix": "fin-table-full",
    "body": [
      "<fin-table",
      "  [rows]=\"${1:rows}\"",
      "  [columns]=\"${2:columns}\"",
      "  [columnMode]=\"${3:FinTableColumnMode.FLEX}\"",
      "  [headerHeight]=\"${4:44}\"",
      "  [rowHeight]=\"${5:64}\"",
      "  [useDefaultSort]=\"${6:false}\"",
      "  [hasBorderRadius]=\"${7:false}\"",
      "  [isHeaderBgTransparent]=\"${8:false}\"",
      "  [hasRowBorder]=\"${9:false}\"",
      "  [hasRowPartialBorder]=\"${10:false}\"",
      "  [hasRowSpacing]=\"${11:false}\"",
      "  [scrollbarH]=\"${12:false}\"",
      "  [scrollbarV]=\"${13:false}\"",
      "  [initialSort]=\"${14:undefined}\"",
      "  [emptyMessage]=\"'${15:No data available}'\">",
      "  <ng-container rowTemplates>",
      "    <ng-template name=\"${16:name}\" [finRowTemplate]=\"${17:rows}\" let-row>",
      "      <div class=\"fin-flex fin-items-center\">",
      "        <fin-avatar-default",
      "          [firstName]=\"row.person.fName\"",
      "          [lastName]=\"row.person.lName\"",
      "          size=\"s\">",
      "        </fin-avatar-default>",
      "        <div class=\"fin-ms-[8px]\">",
      "          {{ row.person.fName }} {{ row.person.lName }}",
      "          <div class=\"fin-text-color-text-tertiary fin-text-[12px]\">",
      "            Description",
      "          </div>",
      "        </div>",
      "      </div>",
      "    </ng-template>",
      "    <!-- Additional templates -->",
      "  </ng-container>",
      "</fin-table>",
    ],
    "description": "Snippet for using the table component",
  },
  "Tabs": {
    "scope": "html",
    "prefix": "fin-tabs",
    "body": [
      "<fin-tabs>",
      "  @for (tab of ${1:tabs}; track tab.id) {",
      "    <fin-tab>",
      "      <ng-template finTabLabel>",
      "        {{ tab.label }}",
      "      </ng-template>",
      "      <ng-template finTabBody>",
      "        {{ tab.content }}",
      "      </ng-template>",
      "    </fin-tab>",
      "  }",
      "</fin-tabs>",
    ],
    "description": "Snippet for using the tabs component with dynamic tabs",
  },
  "Text Area": {
    "scope": "html",
    "prefix": "fin-text-area",
    "body": [
      "<fin-text-area",
      "  [formControl]=\"${1:formControl}\"",
      "  placeholder=\"${2:Placeholder}\"",
      "  label=\"${3:Label}\">",
      "</fin-text-area>",
    ],
    "description": "Snippet for using the text area component with action menu",
  },
  "Text Area - Full": {
    "scope": "html",
    "prefix": "fin-text-area-full",
    "body": [
      "<fin-text-area",
      "  [formControl]=\"${1:formControl}\"",
      "  placeholder=\"${2:Placeholder}\"",
      "  [rows]=\"${3:4}\">",
      "  <ng-container finInputLabel>",
      "    ${4:Label with action}",
      "    <img src=\"/assets/storybook/input/chat-bubble.svg\" alt=\"chat icon\" class=\"fin-w-6 fin-h-6 fin-mx-1 fin-cursor-pointer\" (click)=\"${5:clickFn()}\" />",
      "    <img src=\"/assets/storybook/input/info.svg\" alt=\"info icon\" class=\"fin-w-6 fin-h-6 fin-me-1 fin-cursor-pointer\" title=\"Some title here\" (click)=\"${5:clickFn()}\" />",
      "    <img src=\"/assets/storybook/input/visibility-off.svg\" alt=\"visibility icon\" class=\"fin-w-6 fin-h-6 fin-me-1 fin-cursor-pointer\" (click)=\"${5:clickFn()}\"/>",
      "  </ng-container>",
      "  <ng-container finInputSuffix>",
      "    <button type=\"button\" [finActionMenuTrigger]=\"finMenu.panel\">",
      "      <fin-icon name=\"more_vert\" [size]=\"'l'\"></fin-icon>",
      "    </button>",
      "    <fin-actions-menu #finMenu=\"finActionMenu\">",
      "      <button fin-menu-item>",
      "        <fin-icon class=\"fin-icon\" matIconOutlined name=\"image\"></fin-icon>",
      "        Menu item 1",
      "      </button>",
      "      <button fin-menu-item>",
      "        <fin-icon class=\"fin-icon\" matIconOutlined name=\"image\"></fin-icon>",
      "        Menu item 2",
      "      </button>",
      "    </fin-actions-menu>",
      "  </ng-container>",
      "</fin-text-area>",
    ],
    "description": "Snippet for using the text area component with all of its options",
  },
  "Toolbar": {
    "scope": "html",
    "prefix": "fin-toolbar",
    "body": [
      "<fin-toolbar>",
      "  <fin-tabs #finTabs [type]=\"${1:type}\">",
      "    @for (tab of ${2:tabs}; track tab.id) {",
      "      <fin-tab>",
      "        <ng-template finTabLabel>",
      "          {{ tab.label }}",
      "        </ng-template>",
      "      </fin-tab>",
      "    }",
      "  </fin-tabs>",
      "",
      "  <ng-container finToolbarSuffix></ng-container>",
      "</fin-toolbar>",
    ],
    "description": "Snippet for using the toolbar component with tabs and a slide toggle",
  },
  "Warning Message": {
    "scope": "html",
    "prefix": "fin-warning-message",
    "body": [
      "<fin-warning-message",
      "  [label]=\"${1:Warning message}\">",
      "</fin-warning-message>",
    ],
    "description": "Snippet for using the warning message component.",
  },
  "Zoom Bar": {
    "scope": "html",
    "prefix": "fin-zoom-bar",
    "body": [
      "<fin-zoom-bar",
      "  [formControl]=\"${1:formControl}\"",
      "  [min]=\"${2:min}\"",
      "  [max]=\"${3:max}\"",
      "  [step]=\"${4:step}\">",
      "</fin-zoom-bar>",
    ],
    "description": "Snippet for using the zoom bar component.",
  },
}
