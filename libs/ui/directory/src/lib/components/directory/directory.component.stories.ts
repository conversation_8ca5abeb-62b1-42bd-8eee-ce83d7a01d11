import { CommonModule } from '@angular/common';
import { FinActionsMenuModule } from '@fincloud/ui/actions-menu';
import { FinButtonModule } from '@fincloud/ui/button';
import { FinCheckboxModule } from '@fincloud/ui/checkbox';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinMenuItemModule } from '@fincloud/ui/menu-item';
import { FinTruncateTextModule } from '@fincloud/ui/truncate-text';
import { moduleMetadata, type Meta, type StoryObj } from '@storybook/angular';
import { FinDirectoryComponent } from './directory.component';

const meta: Meta<FinDirectoryComponent> = {
  component: FinDirectoryComponent,
  title: 'Components/Directory',
  parameters: {
    docs: {
      description: {
        component:
          '`import { FinDirectoryModule } from "@fincloud/ui/directory"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=28132-1823&t=hVljdxDT9COnYFOi-4',
    },
  },
  decorators: [
    moduleMetadata({
      imports: [
        CommonModule,
        FinButtonModule,
        FinActionsMenuModule,
        FinIconModule,
        FinMenuItemModule,
        FinTruncateTextModule,
        FinCheckboxModule,
      ],
    }),
  ],
};
export default meta;
type Story = StoryObj<FinDirectoryComponent>;

const baseTemplate = (label: string, showActions = true) => `
  <fin-directory
    class="fin-max-w-[30rem]"
    [selected]="selected"
    [disabled]="disabled"
  >
    <ng-container finSuffix>
      <div finTruncateText class="fin-text-body-2-strong">
      ${label}
    </div>

    <div class="fin-flex fin-justify-between fin-items-center">
      <div class="fin-text-body-3-moderate">23 items</div>
    ${
      showActions
        ? `
        <div>
          <button
            type="button"
            (click)="$event.stopPropagation()"
            [finActionMenuTrigger]="finMenu.panel"
          >
            <fin-icon size='s' name="more_vert"></fin-icon>
          </button>
          <fin-actions-menu #finMenu="finActionMenu">
            <button fin-menu-item size="m" iconName="image">
              <ng-container finMenuItemTitle>Menu item 1</ng-container>
            </button>
            <button fin-menu-item size="m" iconName="image" disabled>
              <ng-container finMenuItemTitle>Menu item 2</ng-container>
            </button>
          </fin-actions-menu>
        </div>
        `
        : ''
    }
      </div>
    </ng-container>
  </fin-directory>
`;

export const Default: Story = {
  args: {
    selected: false,
    disabled: false,
  },
  render: (args) => ({
    props: args,
    template: baseTemplate(
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do',
    ),
  }),
};

export const ViewOnly: Story = {
  render: (args) => ({
    props: args,
    template: baseTemplate('Lorem ipsum', false),
  }),
};

export const Selected: Story = {
  args: {
    ...Default.args,
    selected: true,
  },
  render: (args) => ({
    props: args,
    template: baseTemplate('Lorem ipsum', false),
  }),
};

export const Disabled: Story = {
  args: {
    ...Default.args,
    disabled: true,
  },
  render: (args) => ({
    props: args,
    template: baseTemplate('Lorem ipsum', false),
  }),
};

export const WithCheckBox: Story = {
  args: {
    ...Default.args,
  },
  render: (args) => ({
    props: args,
    template: `
      <fin-directory class="fin-max-w-[30rem]">
        <ng-container finSuffix>
          <div class="fin-flex fin-justify-between fin-items-center fin-gap-[1.2rem]">
            <div finTruncateText class="fin-text-body-2-strong">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do
            </div>

            <fin-checkbox size="s"></fin-checkbox>
          </div>

          <div class="fin-flex fin-justify-between fin-items-center">
            <div class="fin-text-body-3-moderate">23 items</div>

            <button fin-button-action>
              <fin-icon size="s" name="download"></fin-icon>
            </button>   
          </div>
        </ng-container>
      </fin-directory>
    `,
  }),
};
