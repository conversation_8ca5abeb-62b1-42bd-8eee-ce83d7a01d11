import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FinFileUploadComponent } from './components/file-upload/file-upload.component';
import { FinMarkAllAsTouchedDirective } from '@fincloud/utils/directives';
import { FinFormService } from '@fincloud/utils/services';

@NgModule({
  imports: [
    CommonModule,
    FinFileUploadComponent,
    FinMarkAllAsTouchedDirective,
  ],
  exports: [FinFileUploadComponent, FinMarkAllAsTouchedDirective],
  providers: [FinFormService],
})
export class FinFileUploadModule {}
