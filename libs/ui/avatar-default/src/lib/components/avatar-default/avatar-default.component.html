@if (state$ | async; as state) {
  <div
    class="fin-avatar fin-rounded-full fin-bg-color-background-tertiary-strong fin-flex fin-items-center fin-justify-center fin-relative fin-overflow-hidden"
    [ngClass]="'fin-avatar-' + state.size"
  >
    <img
      [src]="state.image"
      class="fin-avatar-img fin-object-cover fin-size-full"
      alt="avatar"
      (error)="onImageError()"
      (load)="onImageLoad()"
      [class.fin-hidden]="!state.imageLoaded"
    />
    @if (!state.imageLoaded) {
      <div
        class="fin-avatar-letters fin-font-medium fin-uppercase fin-text-color-text-light"
        [class.fin-hidden]="state.imageLoaded"
      >
        {{ state.firstNameLetter }}{{ state.lastNameLetter }}
      </div>
    }

    @if (
      state.icon &&
      !state.firstNameLetter &&
      !state.lastNameLetter &&
      !state.image
    ) {
      <fin-icon
        [name]="state.icon"
        [size]="state.size"
        class="fin-text-color-icons-light"
      ></fin-icon>
    }
  </div>
}
