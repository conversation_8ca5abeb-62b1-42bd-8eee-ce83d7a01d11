import figma, { html } from '@figma/code-connect/html';
import { FinSize } from '@fincloud/ui/types';

figma.connect(
  'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=5133-17893',
  {
    props: {
      size: figma.enum('Size', {
        XXL: FinSize.XXL,
        XL: FinSize.XL,
        L: FinSize.L,
        M: FinSize.M,
        S: FinSize.S,
        XS: FinSize.XS,
      }),
    },
    example: (props) =>
      html`<fin-avatar-default
        firstName="John"
        lastName="Doe"
        image=""
        icon=""
        size="${props.size}"
      ></fin-avatar-default>`,
    imports: ["import { FinAvatarModule } from '@fincloud/ui/avatar-default'"],
  },
);
