import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  Input,
  OnChanges,
  SimpleChanges,
} from '@angular/core';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinSize } from '@fincloud/ui/types';
import { BehaviorSubject, Observable, merge, scan, shareReplay } from 'rxjs';
import { AvatarState } from '../../models/avatar-state';

/**
 * Displays an avatar image.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-avatar-default--docs Storybook Reference}
 */
@Component({
  selector: 'fin-avatar-default',
  standalone: true,
  imports: [CommonModule, FinIconModule],
  templateUrl: './avatar-default.component.html',
  styleUrl: './avatar-default.component.scss',
  host: {
    class: 'fin-avatar-default',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinAvatarDefaultComponent implements OnChanges {
  /** User first name. */
  @Input() firstName: string | null = '';

  /** User last name. */
  @Input() lastName: string | null = '';

  /** Size of the avatar. */
  @Input() size: FinSize = FinSize.XXL;

  /** Source of the avatar, can be URL or Base64 format. */
  @Input() image = '';

  /** Name of an icon within a [Material Icons](https://fonts.google.com/icons) font set. */
  @Input() icon = '';

  // ----- Reactive state management (scan pattern like FinTooltipDirective) -----
  private state: AvatarState = {
    firstNameLetter: this.getFirstLetter(this.firstName),
    lastNameLetter: this.getFirstLetter(this.lastName),
    size: this.size,
    image: this.image,
    icon: this.icon,
    imageLoaded: false,
  };

  private programmaticEvent$$ = new BehaviorSubject<Partial<AvatarState>>({});

  // Native events: image load/error mapped to state patches
  private imageEvents$$ = new BehaviorSubject<Partial<AvatarState>>({});
  private nativeEvents$: Observable<Partial<AvatarState>> =
    this.imageEvents$$.asObservable();

  // External events: input changes pushed programmatically
  private externalEvents$ = this.programmaticEvent$$.asObservable();

  protected state$: Observable<AvatarState> = merge(
    this.nativeEvents$,
    this.externalEvents$,
  ).pipe(
    scan((state, command) => {
      // Merge incoming patches; letters should already be derived in ngOnChanges/setters
      return { ...state, ...command };
    }, this.state),
    shareReplay({ bufferSize: 1, refCount: true }),
  );

  ngOnChanges(changes: SimpleChanges): void {
    const nextState = this.extractLastChanges(changes);
    this.updateState(nextState);
  }

  protected onImageLoad() {
    this.imageEvents$$.next({ imageLoaded: true });
  }

  protected onImageError() {
    this.imageEvents$$.next({ imageLoaded: false });
  }

  private updateState(state: Partial<AvatarState>) {
    this.programmaticEvent$$.next(state);
  }

  private extractLastChanges(changes: SimpleChanges): Partial<AvatarState> {
    const patch: Partial<AvatarState> = {};

    if (changes['firstName']) {
      patch.firstNameLetter = this.getFirstLetter(
        changes['firstName'].currentValue,
      );
    }
    if (changes['lastName']) {
      patch.lastNameLetter = this.getFirstLetter(
        changes['lastName'].currentValue,
      );
    }
    if (changes['size']) {
      patch.size = changes['size'].currentValue as FinSize;
    }
    if (changes['image']) {
      patch.image = changes['image'].currentValue as string;
    }
    if (changes['icon']) {
      patch.icon = changes['icon'].currentValue as string;
    }

    return patch;
  }

  private getFirstLetter(value: string | null | undefined): string {
    if (!value) return '';
    const trimmed = String(value).trim();
    return trimmed ? (trimmed[0]?.toUpperCase() ?? '') : '';
  }
}
