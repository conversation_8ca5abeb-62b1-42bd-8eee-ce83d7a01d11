import figma, { html } from '@figma/code-connect/html';
import { FinSize } from '@fincloud/ui/types';

figma.connect(
  'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=5900-6133',
  {
    props: {
      size: figma.enum('Size', {
        M: FinSize.M,
        L: FinSize.L,
      }),
    },
    example: (props) =>
      html`<fin-input
        [size]="${props.size}"
        [label]="label"
        [placeholder]="placeholder"
        [type]="type"
      ></fin-input>`,
    imports: ["import { FinInputModule } from '@fincloud/ui/input'"],
  },
);
