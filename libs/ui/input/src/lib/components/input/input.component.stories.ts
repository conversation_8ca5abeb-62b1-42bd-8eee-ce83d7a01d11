import { CommonModule } from '@angular/common';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { FinIconModule } from '@fincloud/ui/icon';
import { moduleMetadata, type Meta, type StoryObj } from '@storybook/angular';

import { FinAiSuggestionModule } from '@fincloud/ui/ai-suggestion';

import { FinButtonModule } from '@fincloud/ui/button';
import { FinFieldMessageModule } from '@fincloud/ui/field-message';
import { FinTruncateTextModule } from '@fincloud/ui/truncate-text';
import { FinSize } from '@fincloud/ui/types';
import { NgxCurrencyInputMode } from 'ngx-currency';
import { FinInputType } from '../../enums/fin-input-type';
import { LocaleId } from '../../enums/fin-locale-id';
import { FIN_CURRENCY_MASK } from '../../utils/fin-currency-mask-token';
import { FIN_DECIMAL_MASK } from '../../utils/fin-decimal-mask-token';
import { FIN_INTEGER_MASK } from '../../utils/fin-integer-mask-token';
import { FIN_MONTHS_MASK } from '../../utils/fin-months-mask-token';
import { FIN_PERCENTAGE_MASK } from '../../utils/fin-percentage-mask-token';
import { FIN_REGION_LOCALE_ID } from '../../utils/fin-region-locale-id';
import { FinInputModule } from './../../input.module';
import { FinInputComponent } from './input.component';

const MASK_CONFIG_BASE = {
  [LocaleId.DE]: {
    align: 'left',
    allowNegative: false,
    allowZero: true,
    decimal: ',',
    thousands: '.',
    nullable: true,
    min: null,
    max: null,
    inputMode: NgxCurrencyInputMode.Natural,
    precision: 0,
    prefix: '',
    suffix: '',
  },
  [LocaleId.EN]: {
    align: 'left',
    allowNegative: false,
    allowZero: true,
    decimal: '.',
    thousands: ',',
    nullable: true,
    min: null,
    max: null,
    inputMode: NgxCurrencyInputMode.Natural,
    precision: 0,
    prefix: '',
    suffix: '',
  },
};
const CURRENCY_MASK_CONFIG = {
  [LocaleId.DE]: {
    ...MASK_CONFIG_BASE[LocaleId.DE],
    precision: 2,
    suffix: ' €',
  },
  [LocaleId.EN]: {
    ...MASK_CONFIG_BASE[LocaleId.EN],
    precision: 2,
    prefix: '€',
  },
};
const PERCENTAGE_MASK_CONFIG = {
  [LocaleId.DE]: {
    ...MASK_CONFIG_BASE[LocaleId.DE],
    precision: 3,
    suffix: ' %',
  },
  [LocaleId.EN]: {
    ...MASK_CONFIG_BASE[LocaleId.EN],
    precision: 3,
    suffix: ' %',
  },
};
const DECIMAL_MASK_CONFIG = {
  [LocaleId.DE]: {
    ...MASK_CONFIG_BASE[LocaleId.DE],
    precision: 2,
  },
  [LocaleId.EN]: {
    ...MASK_CONFIG_BASE[LocaleId.EN],
    precision: 2,
  },
};

const meta: Meta = {
  component: FinInputComponent,
  title: 'Fields/Input',
  parameters: {
    docs: {
      description: {
        component:
          '`import { FinInputModule, FinFieldMessageModule } from "@fincloud/ui/input"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=5900-6133',
    },
  },
  decorators: [
    moduleMetadata({
      imports: [
        CommonModule,
        FinInputModule,
        FinFieldMessageModule,
        ReactiveFormsModule,
        FinIconModule,
        FinTruncateTextModule,
        FinAiSuggestionModule,
        FinButtonModule,
      ],
      providers: [
        {
          provide: FIN_CURRENCY_MASK,
          useValue: CURRENCY_MASK_CONFIG,
        },
        {
          provide: FIN_PERCENTAGE_MASK,
          useValue: PERCENTAGE_MASK_CONFIG,
        },
        {
          provide: FIN_DECIMAL_MASK,
          useValue: DECIMAL_MASK_CONFIG,
        },
        {
          provide: FIN_MONTHS_MASK,
          useValue: MASK_CONFIG_BASE,
        },
        {
          provide: FIN_INTEGER_MASK,
          useValue: MASK_CONFIG_BASE,
        },
        {
          provide: FIN_REGION_LOCALE_ID,
          useValue: LocaleId.DE,
        },
      ],
    }),
  ],
};
export default meta;
type Story = StoryObj<
  FinInputComponent & {
    finInputLabel: string;
    finInputPrefix: string;
    finInputSuffix: string;
    finInputHint: string;
    formControl: FormControl;
  }
>;

const disabledField = new FormControl({ value: 'Input text', disabled: true });
const invalidField = new FormControl('Input text', Validators.minLength(300));
invalidField.markAsTouched();
const validField = new FormControl('Input text', Validators.required);
validField.updateValueAndValidity();
validField.markAsTouched();

const searchField = new FormControl('Some search text');

export const Primary: Story = {
  args: {
    /** The blur event fires when an element has lost focus */
    // blurred: fn(),
    label: 'Input label',
    placeholder: 'Input text',
  },
  argTypes: {
    label: {
      control: { type: 'text' },
    },
    placeholder: {
      control: { type: 'text' },
    },
    readonly: {
      control: 'boolean',
    },
    size: {
      options: [FinSize.M, FinSize.L],
      control: { type: 'select' },
    },
    type: {
      options: Object.values(FinInputType),
      control: { type: 'select' },
    },
    showPasswordButton: {
      control: 'boolean',
    },
    maxLength: {
      control: { type: 'number' },
    },
    finInputLabel: {
      description: 'Place the label with addition icons next to it',
    },
    finInputPrefix: {
      description: 'Place icons before the input',
    },
    finInputSuffix: {
      description: 'Place icons after the input',
    },
    finInputHint: {
      description: 'Place for a hint text',
    },
    finInputType: {
      table: {
        disable: true,
      },
    },
    passwordTypeToggle: {
      table: {
        disable: true,
      },
    },
  },
  render: (args) => ({
    props: { ...args, formControl: new FormControl('') },
  }),
};

export const ValidationSuccess: Story = {
  argTypes: {},
  args: {
    ...Primary.args,
  },
  render: (args) => {
    const formControl = new FormControl('');
    formControl.markAsTouched();

    return {
      props: {
        ...args,
        formControl,
      },
      template: `
        <fin-input
          [label]="label"
          [formControl]="formControl"

        >
          <fin-field-messages>
            <ng-template finFieldMessage type="success">
              Some success message.
            </ng-template>
          </fin-field-messages>
        </fin-input>
      `,
    };
  },
};

export const ValidationError: Story = {
  argTypes: {},
  args: {
    ...Primary.args,
  },
  render: (args) => {
    const formControl = new FormControl(null, [Validators.required]);
    formControl.markAsTouched();

    return {
      props: {
        ...args,
        formControl,
      },
      template: `
        <fin-input
          [label]="label"
          [formControl]="formControl"

        >
          <fin-field-messages>
            <ng-template finFieldMessage type="error" errorKey="required">
              Some error message.
            </ng-template>
          </fin-field-messages>
        </fin-input>
      `,
    };
  },
};

export const ValidationWarning: Story = {
  argTypes: {},
  args: {
    ...Primary.args,
  },
  render: (args) => {
    const formControl = new FormControl(null, [Validators.requiredTrue]);
    formControl.markAsTouched();
    return {
      props: {
        ...args,
        formControl,
      },
      template: `
        <fin-input
          [label]="label"
          [formControl]="formControl"
        >
          <fin-field-messages>
            <ng-template finFieldMessage type="warning" errorKey="required">
              Some warning message.
            </ng-template>
          </fin-field-messages>
        </fin-input>
      `,
    };
  },
};

export const CustomLabel: Story = {
  argTypes: {},
  args: {
    ...Primary.args,
  },

  render: (args) => {
    return {
      props: {
        ...args,
        formControl: new FormControl(''),
        disabledField,
        clickFn: function () {
          alert('Btn clicked!');
        },
      },
      template: `
        <fin-input
          [formControl]="formControl"
        >
          <ng-container finInputLabel>
            <label class="fin-w-[16rem] fin-me-size-spacing-8" finTruncateText>
              Label with a few multiple actions
            </label>

            <div class="fin-flex fin-gap-size-spacing-8">
              <button fin-button-action (click)="clickFn()">
                <fin-icon src="/assets/storybook/input/chat-bubble.svg"></fin-icon>
              </button>

              <button fin-button-action (click)="clickFn()">
                <fin-icon src="/assets/storybook/input/info.svg"></fin-icon>
              </button>

              <button fin-button-action (click)="clickFn()">
                <fin-icon src="/assets/storybook/input/visibility-off.svg"></fin-icon>
              </button>
            </div>
          </ng-container>
        </fin-input>
      `,
    };
  },
};

export const CustomLabelVariants: Story = {
  argTypes: {},
  args: {
    ...Primary.args,
  },

  render: (args) => {
    return {
      props: {
        ...args,
        disabledField,
        formControl: new FormControl(''),
        clickFn: function () {
          alert('Btn clicked!');
        },
      },
      template: `
        <div class="fin-grid fin-grid-cols-2 fin-gap-4 fin-mb-5">
          <div class="fin-text-center">
            <div
              class="fin-bg-color-background-disabled fin-px-3 fin-py-1 fin-rounded-full fin-text-body-3-moderate fin-text-center fin-inline-block"
            >
              Label with icon
            </div>
          </div>
          <div class="fin-text-center">
            <div
              class="fin-bg-color-background-disabled fin-px-3 fin-py-1 fin-rounded-full fin-text-body-3-moderate fin-text-center fin-inline-block"
            >
              Custom label
            </div>
          </div>
        </div>
        <div class="fin-grid fin-grid-cols-2 fin-gap-4">
          <div class="fin-text-center">
            <fin-input
              [formControl]="formControl"
            >
              <ng-container finInputLabel>
                Label with action

                <div class="fin-flex fin-gap-size-spacing-8 fin-ms-size-spacing-8">
                  <button fin-button-action (click)="clickFn()">
                    <fin-icon src="/assets/storybook/input/chat-bubble.svg"></fin-icon>
                  </button>

                  <button fin-button-action (click)="clickFn()">
                    <fin-icon src="/assets/storybook/input/info.svg"></fin-icon>
                  </button>

                  <button fin-button-action (click)="clickFn()">
                    <fin-icon src="/assets/storybook/input/visibility-off.svg"></fin-icon>
                  </button>
                </div>
              </ng-container>
            </fin-input>
          </div>
          <div class="fin-text-center">
            <fin-input
              [placeholder]="placeholder"
              [formControl]="formControl"
            >
              <ng-container finInputLabel>
                <span class="fin-bg-color-status-success fin-text-color-brand-primary fin-px-2 fin-font-bold fin-rounded-md">Interactive label</span>
              </ng-container>
            </fin-input>
          </div>
        </div>`,
    };
  },
};

export const PasswordType: Story = {
  argTypes: {},
  args: {
    ...Primary.args,
  },

  render: (args) => {
    return {
      props: {
        label: 'Password',
        formControl: new FormControl('somepassword'),
        type: FinInputType.PASSWORD,
      },
    };
  },
};

export const PasswordTypeVariants: Story = {
  argTypes: {},
  args: {
    ...Primary.args,
  },

  render: (args) => {
    return {
      props: {
        ...args,
        formControl: new FormControl('somepassword'),
        disabledField,
        inputTypes: FinInputType,
      },
      template: `
        <div class="fin-grid fin-grid-cols-3 fin-gap-4 fin-mb-5">
          <div class="fin-text-center">
            <div
              class="fin-bg-color-background-disabled fin-px-3 fin-py-1 fin-rounded-full fin-text-body-3-moderate fin-text-center fin-inline-block"
            >
              Input type password
            </div>
          </div>
          <div class="fin-text-center">
            <div
              class="fin-bg-color-background-disabled fin-px-3 fin-py-1 fin-rounded-full fin-text-body-3-moderate fin-text-center fin-inline-block"
            >
              Input type password disabled
            </div>
          </div>
          <div class="fin-text-center">
            <div
              class="fin-bg-color-background-disabled fin-px-3 fin-py-1 fin-rounded-full fin-text-body-3-moderate fin-text-center fin-inline-block"
            >
              Input type password with eye icon
            </div>
          </div>
        </div>
        <div class="fin-grid fin-grid-cols-3 fin-gap-4">
          <div class="fin-text-center">
            <fin-input
              [formControl]="formControl"
              [type]="inputTypes.Password"
            >
            </fin-input>
          </div>
          <div class="fin-text-center">
            <fin-input
              [formControl]="disabledField"
              [type]="inputTypes.Password"
            ></fin-input>
          </div>
          <div class="fin-text-center">
            <fin-input
              [formControl]="formControl"
              [type]="inputTypes.Password"
              [showPasswordButton]="false"
            >
            </fin-input>
          </div>
        </div>`,
    };
  },
};

export const PrefixFields: Story = {
  argTypes: {},
  args: {
    ...Primary.args,
  },

  render: (args) => {
    return {
      props: {
        ...args,
        formControl: new FormControl(''),
      },
      template: `
        <fin-input
          label="Input with prefix"
          [formControl]="formControl"
        >
          <ng-container finInputPrefix>
            <fin-icon src="/assets/storybook/input/de-flag.svg"></fin-icon>
          </ng-container>
        </fin-input>
      `,
    };
  },
};

export const SuffixFields: Story = {
  argTypes: {},
  args: {
    ...Primary.args,
  },

  render: (args) => {
    return {
      props: {
        ...args,
        formControl: searchField,
        disabledField,
        inputTypes: FinInputType,
        searchFn: () => {
          alert(`Submit with data: ${searchField.value}`);
        },
        clearFn: () => {
          searchField.setValue('');
        },
      },
      template: `
        <fin-input
          label="Input with suffix"
          [formControl]="formControl"
        >
          <ng-container finInputSuffix>
            <fin-icon name="search"></fin-icon>
          </ng-container>
        </fin-input>
      `,
    };
  },
};

export const FieldsWithPrefixAndSuffix: Story = {
  argTypes: {},
  args: {
    ...Primary.args,
  },

  render: (args) => {
    return {
      props: {
        ...args,
        searchControl: searchField,
        formControl: new FormControl(''),
        disabledField,
        inputTypes: FinInputType,
        searchFn: () => {
          alert(`Submit with data: ${searchField.value}`);
        },
        clearFn: () => {
          searchField.setValue('');
        },
      },
      template: `
        <div class="fin-grid fin-grid-cols-4 fin-gap-4 fin-mb-5">
          <div class="fin-text-center">
            <div
              class="fin-bg-color-background-disabled fin-px-3 fin-py-1 fin-rounded-full fin-text-body-3-moderate fin-text-center fin-inline-block"
            >
              Input with length counter for suffix
            </div>
          </div>
          <div class="fin-text-center">
            <div
              class="fin-bg-color-background-disabled fin-px-3 fin-py-1 fin-rounded-full fin-text-body-3-moderate fin-text-center fin-inline-block"
            >
              Input with more then one functional icons for suffix
            </div>
          </div>
          <div class="fin-text-center">
            <div
              class="fin-bg-color-background-disabled fin-px-3 fin-py-1 fin-rounded-full fin-text-body-3-moderate fin-text-center fin-inline-block"
            >
              Input with prefix icon
            </div>
          </div>
          <div class="fin-text-center">
            <div
              class="fin-bg-color-background-disabled fin-px-3 fin-py-1 fin-rounded-full fin-text-body-3-moderate fin-text-center fin-inline-block"
            >
              Input with prefix and suffix icon
            </div>
          </div>
        </div>
        <div class="fin-grid fin-grid-cols-4 fin-gap-4">
          <div class="fin-text-center">
            <fin-input
              label="Input label"
              [formControl]="formControl"
            >
              <ng-container finInputSuffix>{{ formControl.value.length }}</ng-container>
            </fin-input>
          </div>
          <div class="fin-text-center">
            <fin-input
              label="Search"
              [formControl]="searchControl"
            >
              <ng-container finInputSuffix>
                <div class="fin-flex fin-gap-x-[0.4rem]">
                  <button fin-button-action size="m" [ngClass]="{'fin-invisible': !searchControl.value }" (click)="clearFn()">
                   <fin-icon name="close"></fin-icon>
                  </button>
                  <button fin-button-action size="m" (click)="clickFn()">
                    <fin-icon name="search"></fin-icon>
                  </button>
                </div>
              </ng-container>
            </fin-input>
          </div>
          <div class="fin-text-center">
            <fin-input
              label="Prefix icon"
              [formControl]="formControl"
            >
              <ng-container finInputPrefix>
                <fin-icon src="/assets/storybook/input/de-flag.svg"></fin-icon>
              </ng-container>
            </fin-input>
          </div>
          <div class="fin-text-center">
            <fin-input
              label="Prefix and suffix"
              [formControl]="formControl"
            >
              <ng-container finInputPrefix>
                <fin-icon src="/assets/storybook/input/de-flag.svg"></fin-icon>
              </ng-container>
              <ng-container finInputSuffix>
                <fin-icon name="close"></fin-icon>
              </ng-container>
            </fin-input>
          </div>
        </div>`,
    };
  },
};

export const Hint: Story = {
  argTypes: {},
  args: {
    ...Primary.args,
  },

  render: (args) => {
    return {
      props: {
        ...args,
        formControl: new FormControl(''),
      },
      template: `
        <fin-input
          label="Input with hint"
          [formControl]="formControl"
        >
          <ng-container finInputHint>
            Some hint text
          </ng-container>
        </fin-input>
      `,
    };
  },
};

export const LoaderField: Story = {
  argTypes: {},
  args: {
    ...Primary.args,
  },

  render: (args) => {
    return {
      props: {
        ...args,
        formControl: new FormControl(''),
      },
      template: `
        <fin-input
          label="Input with loader"
          [formControl]="formControl"
          [showLoader]="true"
        ></fin-input>
      `,
    };
  },
};

export const CurrencyMask: Story = {
  argTypes: {},
  args: {
    ...Primary.args,
  },

  render: (args) => {
    return {
      props: {
        ...args,
        formControl: new FormControl(''),
      },
      template: `
        <fin-input
          type="currency"
          label="With currency mask"
          [formControl]="formControl"
        >
        </fin-input>
      `,
    };
  },
};

export const PercentageMask: Story = {
  argTypes: {},
  args: {
    ...Primary.args,
  },

  render: (args) => {
    return {
      props: {
        ...args,
        formControl: new FormControl(''),
      },
      template: `
        <fin-input
          type="percentage"
          label="With percentage mask"
          [formControl]="formControl"
        >
        </fin-input>
      `,
    };
  },
};

export const Readonly: Story = {
  argTypes: {},
  args: {
    ...Primary.args,
  },
  render: (args) => {
    return {
      props: {
        ...args,
        formControl: new FormControl('Some readonly data'),
      },
      template: `
        <fin-input
          type="text"
          label="Readonly field"
          [formControl]="formControl"
          readonly="true"
        >
        </fin-input>
      `,
    };
  },
};

export const AiSuggestion: Story = {
  argTypes: {},
  args: {
    ...Primary.args,
  },
  render: (args) => {
    const formControl = new FormControl('Some readonly data');
    const form = new FormGroup({ formControl });
    return {
      props: {
        ...args,
        formControl,
        form,
        suggest: () => formControl.setValue('New value'),
      },
      template: `
        <form [formGroup]="form">
          <fin-input
            type="text"
            label="Field with AI suggestion"
            formControlName="formControl"
          >
            <fin-ai-suggestion enabled formControlName="formControl"></fin-ai-suggestion>
          </fin-input>

          <button fin-button size="m" shape="round" appearance="stealth" class="fin-mt-6" (click)="suggest()">
            <fin-icon src="assets/storybook/icons/ai.svg"></fin-icon>
            Generate
          </button>
        </form>
      `,
    };
  },
};

export const Disabled: Story = {
  argTypes: {},
  args: {
    ...Primary.args,
  },
  render: (args) => {
    const formControl = new FormControl('Some readonly data');
    formControl.disable();
    return {
      props: {
        ...args,
        formControl,
      },
      template: `
        <fin-input
          type="text"
          label="Readonly field"
          [formControl]="formControl"
        >
        </fin-input>
      `,
    };
  },
};
