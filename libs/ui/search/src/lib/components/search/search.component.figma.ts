import figma, { html } from '@figma/code-connect/html';
import { FinSize } from '@fincloud/ui/types';

figma.connect(
  'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=5900-6195',
  {
    props: {
      size: figma.enum('Size', {
        M: FinSize.M,
        L: FinSize.L,
      }),
    },
    example: (props) =>
      html`<fin-search
        placeholder=""
        size="${props.size}"
        [showLoader]="showLoader"
        [autocomplete]="autocomplete"
        [numberOfResults]="numberOfResults"
      ></fin-search>`,
    imports: ["import { FinSearchModule } from '@fincloud/ui/search'"],
  },
);
