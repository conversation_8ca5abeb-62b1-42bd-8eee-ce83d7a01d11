import figma, { html } from '@figma/code-connect/html';
import { FinSize } from '@fincloud/ui/types';
import { FinButtonActionType } from '../../enums/fin-button-action-type';
import { FinButtonShape } from '../../enums/fin-button-shape';

figma.connect(
  'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=26593-18280',
  {
    props: {
      size: figma.enum('Size', {
        'XS (12)': FinSize.XS,
        'S (16)': FinSize.S,
        'M (20)': FinSize.M,
        'L (24)': FinSize.L,
        'XL (32)': FinSize.XL,
        'XXL (48)': FinSize.XXL,
      }),
      shape: figma.enum('Shape', {
        Round: FinButtonShape.ROUND,
        Rectangle: FinButtonShape.RECTANGLE,
      }),
      type: figma.enum('Type', {
        Informative: FinButtonActionType.INFORMATIVE,
        Primary: FinButtonActionType.PRIMARY,
        Tertiary: FinButtonActionType.TERTIARY,
      }),
    },
    example: (props) =>
      html`<button
        fin-button-action
        size="${props.size}"
        shape="${props.shape}"
        [actionType]="${props.type}"
      >
        <fin-icon name="account_circle"></fin-icon>
      </button>`,
    imports: ["import { FinButtonModule } from '@fincloud/ui/button'"],
  },
);
