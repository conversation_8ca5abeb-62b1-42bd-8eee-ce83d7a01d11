import figma, { html } from '@figma/code-connect/html';
import { FinSize } from '@fincloud/ui/types';
import { FinButtonAppearance } from '../../enums/fin-button-appearance';
import { FinButtonShape } from '../../enums/fin-button-shape';

figma.connect(
  'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=5210-52825',
  {
    props: {
      size: figma.enum('Size', {
        'XS (18)': FinSize.XS,
        'S (24)': FinSize.S,
        'M (32)': FinSize.M,
        'L (40)': FinSize.L,
        'XL (48)': FinSize.XL,
      }),
      shape: figma.enum('Shape', {
        Round: FinButtonShape.ROUND,
        Rectangle: FinButtonShape.RECTANGLE,
      }),
      appearance: figma.enum('Type', {
        Informative: FinButtonAppearance.INFORMATIVE,
        Primary: FinButtonAppearance.PRIMARY,
        Secondary: FinButtonAppearance.SECONDARY,
        Stealth: FinButtonAppearance.STEALTH,
      }),
      attention: figma.boolean('Attention'),
    },
    example: (props) =>
      html`\
    <button fin-button size="{{${props.size}}}" shape="{{${props.shape}}}" appearance="{{${props.appearance}}}" attention="${props.attention}">Label</button>`,
    imports: ["import { FinButtonModule } from '@fincloud/ui/button'"],
  },
);
