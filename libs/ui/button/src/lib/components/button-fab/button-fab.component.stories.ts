import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { FinActionsMenuModule } from '@fincloud/ui/actions-menu';
import { FinAnimationsModule } from '@fincloud/ui/animations';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinMenuItemModule } from '@fincloud/ui/menu-item';
import { FinTruncateTextModule } from '@fincloud/ui/truncate-text';
import { FinSize } from '@fincloud/ui/types';
import { moduleMetadata, type Meta, type StoryObj } from '@storybook/angular';
import { FinButtonFabComponent } from './button-fab.component';

const meta: Meta<FinButtonFabComponent> = {
  component: FinButtonFabComponent,
  title: 'Components/Buttons/Button Fab',
  decorators: [
    moduleMetadata({
      imports: [
        CommonModule,
        MatButtonModule,
        FinIconModule,
        FinActionsMenuModule,
        FinTruncateTextModule,
        FinAnimationsModule,
        FinMenuItemModule,
      ],
    }),
  ],
  parameters: {
    docs: {
      description: {
        component: '`import { FinButtonModule } from "@fincloud/ui/button"`',
      },
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=8919-27031&m=dev',
    },
  },
};
export default meta;
type Story = StoryObj<FinButtonFabComponent>;

export const Primary: Story = {
  args: {
    size: FinSize.S,
  },
  argTypes: {
    size: {
      control: { type: 'select' },
      options: [FinSize.S, FinSize.M],
    },
  },
  render: (args) => ({
    props: args,
    template: `
      <button fin-button-fab [size]="size">
        <fin-icon src="assets/fabs/svgNeoGptFab.svg"></fin-icon>
      </button>
    `,
  }),
};

export const WithLoadingAnimation: Story = {
  args: {
    ...Primary.args,
  },
  argTypes: {
    ...Primary.argTypes,
  },
  render: (args) => ({
    props: args,
    template: `
        <button fin-button-fab [size]="size">
          <fin-border-sweep class="fin-size-full fin-rounded-full" [enableAnimation]="true">
            <fin-icon src="assets/fabs/svgNeoGptFab.svg"></fin-icon>
          </fin-border-sweep>
        </button>
    `,
  }),
};

export const WithActionMenu: Story = {
  args: {
    ...Primary.args,
  },
  argTypes: {
    ...Primary.argTypes,
  },
  render: (args) => ({
    props: args,
    template: `
      <button fin-button-fab [size]="size" [finActionMenuTrigger]="finMenu.panel">
        <fin-icon src="assets/fabs/svgNeoGptFab.svg"></fin-icon>
      </button>

        <fin-actions-menu
          #finMenu="finActionMenu"
          yPosition="above"
        >
          <button fin-menu-item iconName="image" size="m">
            <ng-container finMenuItemTitle>
              <div finTruncateText class="fin-max-w-[13rem]">
                Menu item text with more symbols
              </div>
            </ng-container>
          </button>

          <button fin-menu-item iconName="image" size="m">
            <ng-container finMenuItemTitle>
              <div finTruncateText class="fin-max-w-[13rem]">
                Menu item text with more symbols
              </div>
            </ng-container>
          </button>
        </fin-actions-menu>
    `,
  }),
};
