import figma, { html } from '@figma/code-connect/html';
import { FinSize } from '@fincloud/ui/types';

figma.connect(
  'https://www.figma.com/design/MhR5TvCS7m9ULuzJPQL2zU/neodesign-System-v.2?node-id=5522-52649',
  {
    props: {
      size: figma.enum('Size', {
        'S (40)': FinSize.S,
        'M (56)': FinSize.M,
      }),
    },
    example: (props) =>
      html`<button fin-button-fab size="${props.size}">
        <fin-icon src="assets/fabs/svgNeoGptFab.svg"></fin-icon>
      </button>`,
    imports: ["import { FinButtonModule } from '@fincloud/ui/button'"],
  },
);
