import {
  DestroyRef,
  Directive,
  ElementRef,
  Host,
  OnInit,
  Optional,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormGroupDirective, NgForm } from '@angular/forms';
import { FinFormService } from '@fincloud/utils/services';
import { fromEvent } from 'rxjs';

@Directive({
  // eslint-disable-next-line @angular-eslint/directive-selector
  selector: 'form',
  standalone: true,
  providers: [FinFormService],
})
export class FinMarkAllAsTouchedDirective implements OnInit {
  constructor(
    private element: ElementRef<HTMLFormElement>,
    private destroyRef: DestroyRef,
    private FinFormService: FinFormService,
    @Optional() @Host() private ngForm: NgForm,
    @Optional() @Host() private formGroupDir: FormGroupDirective,
  ) {}

  ngOnInit(): void {
    fromEvent(this.element.nativeElement, 'submit')
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(() => {
        const form = this.ngForm?.form || this.formGroupDir?.form;

        if (form) {
          this.FinFormService.finMarkAllAsTouched(form);
        }
      });
  }
}
