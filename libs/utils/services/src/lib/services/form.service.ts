import { Injectable } from '@angular/core';
import { AbstractControl, FormArray, FormGroup } from '@angular/forms';

@Injectable()
export class FinFormService {
  finMarkAllAsTouched(form: FormGroup) {
    Object.values(form.controls).forEach((control) =>
      this.finMarkAllAsTouchedRecursive(control),
    );
  }

  private finMarkAllAsTouchedRecursive(control: AbstractControl) {
    control.markAsTouched({ onlySelf: true });
    control.updateValueAndValidity();

    if (control instanceof FormGroup) {
      Object.values(control.controls).forEach((control) =>
        this.finMarkAllAsTouchedRecursive(control),
      );
    } else if (control instanceof FormArray) {
      control.controls.forEach((control) =>
        this.finMarkAllAsTouchedRecursive(control),
      );
    }
  }
}
