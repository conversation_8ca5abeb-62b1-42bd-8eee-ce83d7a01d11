include:
  - template: Jobs/SAST.gitlab-ci.yml
  - template: Jobs/Secret-Detection.gitlab-ci.yml
  - template: Jobs/Dependency-Scanning.gitlab-ci.yml
  - project: devops/templates/ci
    ref: master
    file: jobs/semantic-versioning.gitlab-ci.yaml
  - project: devops/templates/pipelines/docker
    ref: master
    file:
      - base.yaml
  - project: devops/templates/pipelines/helm
    ref: master
    file:
      - base.yaml

workflow:
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: never
    - when: always

stages:
  - lint
  - build
  - test
  - push
  - deploy
  - release

variables:
  SERVICE_BASENAME: lib-ui
  DOCKER_REGISTRY: docker.nexus-new.neoshare.dev
  IMAGE: fincloud/lib-ui
  HELM_REGISTRY: helm.nexus-new.neoshare.dev
  HELM_CHART_DIR: helm-chart

############## -- START TEMPLATES -- ##############

.helm_upgrade_install_chart:
  image: $NEO_CI_IMAGE_PATH/helm:$NEO_CI_IMAGE_TAG
  resource_group: helm-upgrade-install
  before_script:
    - kubectl config get-contexts
    - kubectl config use-context $KUBE_CONTEXT
    - kubectl config set-context --current --namespace $HELM_NAMESPACE
  script:
    - kubectl get pods
    - helm upgrade --install $HELM_RELEASE_NAME oci://$HELM_REGISTRY/$HELM_REPO_NAME/$SERVICE_BASENAME --set image.tag=${CI_COMMIT_SHORT_SHA} --version ${VERSION} --wait

.publish_npm_package:
  stage: release
  image: docker-proxy-v2.neo.loan/node:18
  before_script:
    - git config --global user.email "<EMAIL>"
    - git config --global user.name "CICD"
    - echo -e "machine git.neo.loan\nlogin gitlab-ci-token\npassword ${PROJECT_AUTOMATION_PUSH_TOKEN}" > ~/.netrc
    - chmod 600 ~/.netrc
    - git remote add project-origin https://oauth2:${PROJECT_AUTOMATION_PUSH_TOKEN}@${CI_SERVER_HOST}/${CI_PROJECT_PATH}
    - git checkout $CI_COMMIT_BRANCH
    - git branch
    - npm ci --cache .npm --prefer-offline
    - git config pull.rebase false
    - git pull
  # script:
  #   - npm config set tag=latest
  #   - npx nx release version minor --git-commit=true --git-tag=true
  #   - npm run build
  #   - git pull
  #   - npx nx release publish
  #   - git push project-origin $CI_COMMIT_BRANCH -o ci.skip
  #   - git push --tags project-origin -o ci.skip
  cache:
    key:
      files:
        - package-lock.json
    paths:
      - .npm/

############## -- END TEMPLATES -- ##############

#############
# Pre Stage #
#############

version:
  cache: []
  extends: .version

##############
# Test Stage #
##############

container_scanning:
  stage: test

code_intelligence:
  image: docker-proxy.nexus.neoshare.dev/node:16
  stage: test
  cache: []
  script:
    - npm install -g @sourcegraph/lsif-tsc
    - lsif-tsc -p .
  allow_failure: true
  artifacts:
    reports:
      lsif: dump.lsif
    expire_in: 2 weeks

####################
##-- Push Stage --##
####################

docker-push:
  rules:
    - if: $CI_COMMIT_REF_NAME == "next" || $CI_COMMIT_REF_NAME == "development" || $CI_COMMIT_REF_NAME == "master"

helm-push-chart:
  needs:
    - version
    - docker-push
  rules:
    - if: $CI_COMMIT_REF_NAME == "next" || $CI_COMMIT_REF_NAME == "development" || $CI_COMMIT_REF_NAME == "master"

######################
##-- Deploy Stage --##
######################

deploy-next:
  extends: .helm_upgrade_install_chart
  stage: deploy
  environment:
    name: next
    url: https://lib-ui-next.neoshare.dev
    # kubernetes:
    #   namespace: lib-ui-master
  variables:
    KUBE_CONTEXT: fincloud/cfg-ci-cd:non-production-management
    HELM_RELEASE_NAME: lib-ui-next
    HELM_CHART_VALUES: '--set image.tag=$CI_COMMIT_SHORT_SHA --set gateway.hostnames={lib-ui-next.neoshare.dev} --set global.externalSecrets.vaultPath={lib-ui-next}'
    HELM_NAMESPACE: lib-ui-next
  rules:
    - if: $CI_COMMIT_REF_NAME == "next"

deploy-development:
  extends: .helm_upgrade_install_chart
  stage: deploy
  environment:
    name: development
    url: https://lib-ui-dev.neoshare.dev
    # kubernetes:
    #   namespace: lib-ui-master
  variables:
    KUBE_CONTEXT: fincloud/cfg-ci-cd:non-production-management
    HELM_RELEASE_NAME: lib-ui-development
    HELM_CHART_VALUES: '--set image.tag=$CI_COMMIT_SHORT_SHA --set gateway.hostnames={lib-ui-dev.neoshare.dev} --set global.externalSecrets.vaultPath={lib-ui-dev}'
    HELM_NAMESPACE: lib-ui-development
  rules:
    - if: $CI_COMMIT_REF_NAME == "development"

deploy-master:
  extends: .helm_upgrade_install_chart
  stage: deploy
  environment:
    name: master
    url: https://lib-ui.neoshare.dev
    # kubernetes:
    #   namespace: lib-ui-master
  variables:
    KUBE_CONTEXT: fincloud/cfg-ci-cd:non-production-management
    HELM_RELEASE_NAME: lib-ui-master
    HELM_CHART_VALUES: '--set image.tag=$CI_COMMIT_SHORT_SHA --set gateway.hostnames={lib-ui.neoshare.dev} --set global.externalSecrets.vaultPath={lib-ui-master}'
    HELM_NAMESPACE: lib-ui-master
  rules:
    - if: $CI_COMMIT_REF_NAME == "master"

publish_npm_package_next:
  extends: .publish_npm_package
  script:
    - npm config set tag=next
    - npx nx release version prerelease --preid=next --git-commit=true --git-tag=true
    # - chmod +x ./tools/update-changelog.sh
    # - ./tools/update-changelog.sh
    - npm run build
    - git pull
    - npx nx release publish
    - git push project-origin $CI_COMMIT_BRANCH -o ci.skip
    - git push --tags project-origin -o ci.skip
  rules:
    - if: $CI_COMMIT_BRANCH == "next"

publish_npm_package_rc:
  extends: .publish_npm_package
  script:
    - npm config set tag=rc
    - npx nx release version prerelease --preid=rc --git-commit=true --git-tag=true
    - npm run build
    - git pull
    - npx nx release publish
    - git push project-origin $CI_COMMIT_BRANCH -o ci.skip
    - git push --tags project-origin -o ci.skip
  rules:
    - if: $CI_COMMIT_BRANCH == "development"

publish_npm_package:
  extends: .publish_npm_package
  script:
    - npm config set tag=latest
    - npx nx release version minor --git-commit=true --git-tag=true
    - npm run build
    - git pull
    - npx nx release publish
    - git push project-origin $CI_COMMIT_BRANCH -o ci.skip
    - git push --tags project-origin -o ci.skip
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
