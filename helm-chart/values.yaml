# Default values for lib-ui.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

global:
  externalSecrets:
    enabled: true
    # Change these to match your external secret store configuration
    storeName: lib-ui-oauth2-proxy-store
    storeKind: SecretStore
    pathPrefix: application/annotations
    vaultPath: lib-ui-next

replicaCount: 1

image:
  repository: docker.nexus-new.neoshare.dev/fincloud/lib-ui
  pullPolicy: Always
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Automatically mount a ServiceAccount's API credentials?
  automount: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: {}
podLabels: {}

podSecurityContext: {}
  # fsGroup: 2000

securityContext:
  capabilities:
    drop:
    - ALL
  runAsNonRoot: true
  runAsUser: 101
  runAsGroup: 101

service:
  type: ClusterIP
  port: 8080

ingress:
  enabled: false

gateway:
  enabled: true
  parentRefs:
    name: "private-gateway"
    namespace: "istio-gateways"
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hostnames: []

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

livenessProbe:
  httpGet:
    path: /
    port: http
readinessProbe:
  httpGet:
    path: /
    port: http

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

# Additional volumes on the output Deployment definition.
volumes: []
# - name: foo
#   secret:
#     secretName: mysecret
#     optional: false

# Additional volumeMounts on the output Deployment definition.
volumeMounts: []
# - name: foo
#   mountPath: "/etc/foo"
#   readOnly: true

nodeSelector: {}

tolerations: []

affinity: {}

oauth2-proxy:
  fullnameOverride: lib-ui-auth-oauth2-proxy
  config:
    existingSecret: lib-ui-auth-oauth2-proxy
    configFile: |-
      provider = "azure"
      azure_tenant = "8b16cef5-eef3-4550-aa58-b82b460ee8a8"
      oidc_issuer_url = "https://login.microsoftonline.com/8b16cef5-eef3-4550-aa58-b82b460ee8a8/v2.0"
      email_domains = [ "neoshare.de" ]
      upstreams = [ "http://{{ .Release.Name }}:8080/" ]
      skip_provider_button = true
