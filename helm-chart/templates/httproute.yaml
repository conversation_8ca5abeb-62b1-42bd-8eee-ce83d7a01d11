{{- if .Values.gateway.enabled -}}
{{- $fullName := include "lib-ui.fullname" . -}}
{{- $svcPort := .Values.service.port -}}
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: {{ $fullName }}
  labels:
    {{- include "lib-ui.labels" . | nindent 4 }}
  {{- with .Values.gateway.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  parentRefs:
    - name: {{ .Values.gateway.parentRefs.name }}
      {{- if .Values.gateway.parentRefs.namespace }}
      namespace: {{ .Values.gateway.parentRefs.namespace }}
      {{- end }}
      {{- if .Values.gateway.parentRefs.sectionName }}
      sectionName: {{ .Values.gateway.parentRefs.sectionName }}
      {{- end }}
  hostnames:
    {{- range .Values.gateway.hostnames }}
    - {{ . | quote }}
    {{- end }}
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: "/"
    backendRefs:
    - name: lib-ui-auth-oauth2-proxy
      port: 80
      kind: Service
      group: ""
{{- end }}
