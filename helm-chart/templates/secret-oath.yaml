apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: lib-ui-auth-oauth2-proxy
spec:
  refreshInterval: 1m
  secretStoreRef:
    name: {{ .Values.global.externalSecrets.storeName }}
    kind: {{ .Values.global.externalSecrets.storeKind }}
  target:
    name: lib-ui-auth-oauth2-proxy
  data:
    - secretKey: client-id
      remoteRef:
        key: application/libui/{{ .Values.global.externalSecrets.vaultPath }}/oidc-azure
        property: client-id
    - secretKey: client-secret
      remoteRef:
        key: application/libui/{{ .Values.global.externalSecrets.vaultPath }}/oidc-azure
        property: client-secret
    - secretKey: issuer-url
      remoteRef:
        key: application/libui/{{ .Values.global.externalSecrets.vaultPath }}/oidc-azure
        property: issuer-url
    - secretKey: cookie-secret
      remoteRef:
        key: application/libui/{{ .Values.global.externalSecrets.vaultPath }}/oauth2-proxy
        property: cookie-secret
