import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component } from '@angular/core';
import { FormControl } from '@angular/forms';
import { FinDatePickerModule } from '@fincloud/ui/date-picker';
@Component({
  standalone: true,
  imports: [CommonModule, FinDatePickerModule],
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinAppComponent {
  formControl = new FormControl('');

  ngOnInit() {
    this.formControl.valueChanges.subscribe((value) => {
      console.log(value);
    });
  }
}
