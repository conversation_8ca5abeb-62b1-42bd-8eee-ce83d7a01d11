#!/usr/bin/env bash
set -euo pipefail

# Ensure we have at least two tags. 
if [ "$(git tag | wc -l | tr -d ' ')" -lt 2 ]; then
  echo "❌ Need at least two git tags."
  exit 1
fi

# Get the last two tags in chronological order (by creation date)
prev_tag="$(git tag --sort=creatordate | tail -n2 | head -n1)"
last_tag="$(git tag --sort=creatordate | tail -n1)"

# Read current version from libs/ui/package.json
if [ ! -f "./libs/ui/package.json" ]; then
  echo "❌ ./libs/ui/package.json not found."
  exit 1
fi

current_version="$(node -p "require('./libs/ui/package.json').version" 2>/dev/null || true)"
if [ -z "${current_version}" ]; then
  echo "❌ Could not read version from ./libs/ui/package.json"
  exit 1
fi

# Remove leading 'v' for the changelog title
current_no_v="${current_version#v}"

echo "Prev tag:   ${prev_tag}"
echo "Last tag:   ${last_tag}"
echo "Pkg ver:    ${current_version} (title uses: ${current_no_v})"
echo "----------------------------------------"

cmd="npx nx release changelog \"${current_no_v}\" --from=\"${prev_tag}\" --to=\"${last_tag}\" --git-tag=false"
echo "$cmd"
eval "$cmd"